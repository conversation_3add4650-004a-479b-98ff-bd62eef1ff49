"use strict";exports.id=736,exports.ids=[736],exports.modules={18080:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},39401:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},45817:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},71960:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])},89122:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},91123:(a,b,c)=>{c.d(b,{CC:()=>R,Q6:()=>S,bL:()=>Q,zi:()=>T});var d=c(43616),e=c(21311),f=c(86984),g=c(84677),h=c(37784),i=c(55002),j=c(78309),k=c(20355),l=c(62115),m=c(7957),n=c(88036),o=c(21157),p=["PageUp","PageDown"],q=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],r={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},s="Slider",[t,u,v]=(0,n.N)(s),[w,x]=(0,h.A)(s,[v]),[y,z]=w(s),A=d.forwardRef((a,b)=>{let{name:c,min:g=0,max:h=100,step:j=1,orientation:k="horizontal",disabled:l=!1,minStepsBetweenThumbs:m=0,defaultValue:n=[g],value:r,onValueChange:s=()=>{},onValueCommit:u=()=>{},inverted:v=!1,form:w,...x}=a,z=d.useRef(new Set),A=d.useRef(0),B="horizontal"===k,[C=[],F]=(0,i.i)({prop:r,defaultProp:n,onChange:a=>{let b=[...z.current];b[A.current]?.focus(),s(a)}}),G=d.useRef(C);function H(a,b,{commit:c}={commit:!1}){let d=(String(j).split(".")[1]||"").length,f=function(a,b){let c=Math.pow(10,b);return Math.round(a*c)/c}(Math.round((a-g)/j)*j+g,d),i=(0,e.q)(f,[g,h]);F((a=[])=>{let d=function(a=[],b,c){let d=[...a];return d[c]=b,d.sort((a,b)=>a-b)}(a,i,b);if(!function(a,b){if(b>0)return Math.min(...a.slice(0,-1).map((b,c)=>a[c+1]-b))>=b;return!0}(d,m*j))return a;{A.current=d.indexOf(i);let b=String(d)!==String(a);return b&&c&&u(d),b?d:a}})}return(0,o.jsx)(y,{scope:a.__scopeSlider,name:c,disabled:l,min:g,max:h,valueIndexToChangeRef:A,thumbs:z.current,values:C,orientation:k,form:w,children:(0,o.jsx)(t.Provider,{scope:a.__scopeSlider,children:(0,o.jsx)(t.Slot,{scope:a.__scopeSlider,children:(0,o.jsx)(B?D:E,{"aria-disabled":l,"data-disabled":l?"":void 0,...x,ref:b,onPointerDown:(0,f.m)(x.onPointerDown,()=>{l||(G.current=C)}),min:g,max:h,inverted:v,onSlideStart:l?void 0:function(a){let b=function(a,b){if(1===a.length)return 0;let c=a.map(a=>Math.abs(a-b)),d=Math.min(...c);return c.indexOf(d)}(C,a);H(a,b)},onSlideMove:l?void 0:function(a){H(a,A.current)},onSlideEnd:l?void 0:function(){let a=G.current[A.current];C[A.current]!==a&&u(C)},onHomeKeyDown:()=>!l&&H(g,0,{commit:!0}),onEndKeyDown:()=>!l&&H(h,C.length-1,{commit:!0}),onStepKeyDown:({event:a,direction:b})=>{if(!l){let c=p.includes(a.key)||a.shiftKey&&q.includes(a.key),d=A.current;H(C[d]+j*(c?10:1)*b,d,{commit:!0})}}})})})})});A.displayName=s;var[B,C]=w(s,{startEdge:"left",endEdge:"right",size:"width",direction:1}),D=d.forwardRef((a,b)=>{let{min:c,max:e,dir:f,inverted:h,onSlideStart:i,onSlideMove:k,onSlideEnd:l,onStepKeyDown:m,...n}=a,[p,q]=d.useState(null),s=(0,g.s)(b,a=>q(a)),t=d.useRef(void 0),u=(0,j.jH)(f),v="ltr"===u,w=v&&!h||!v&&h;function x(a){let b=t.current||p.getBoundingClientRect(),d=P([0,b.width],w?[c,e]:[e,c]);return t.current=b,d(a-b.left)}return(0,o.jsx)(B,{scope:a.__scopeSlider,startEdge:w?"left":"right",endEdge:w?"right":"left",direction:w?1:-1,size:"width",children:(0,o.jsx)(F,{dir:u,"data-orientation":"horizontal",...n,ref:s,style:{...n.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:a=>{let b=x(a.clientX);i?.(b)},onSlideMove:a=>{let b=x(a.clientX);k?.(b)},onSlideEnd:()=>{t.current=void 0,l?.()},onStepKeyDown:a=>{let b=r[w?"from-left":"from-right"].includes(a.key);m?.({event:a,direction:b?-1:1})}})})}),E=d.forwardRef((a,b)=>{let{min:c,max:e,inverted:f,onSlideStart:h,onSlideMove:i,onSlideEnd:j,onStepKeyDown:k,...l}=a,m=d.useRef(null),n=(0,g.s)(b,m),p=d.useRef(void 0),q=!f;function s(a){let b=p.current||m.current.getBoundingClientRect(),d=P([0,b.height],q?[e,c]:[c,e]);return p.current=b,d(a-b.top)}return(0,o.jsx)(B,{scope:a.__scopeSlider,startEdge:q?"bottom":"top",endEdge:q?"top":"bottom",size:"height",direction:q?1:-1,children:(0,o.jsx)(F,{"data-orientation":"vertical",...l,ref:n,style:{...l.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:a=>{let b=s(a.clientY);h?.(b)},onSlideMove:a=>{let b=s(a.clientY);i?.(b)},onSlideEnd:()=>{p.current=void 0,j?.()},onStepKeyDown:a=>{let b=r[q?"from-bottom":"from-top"].includes(a.key);k?.({event:a,direction:b?-1:1})}})})}),F=d.forwardRef((a,b)=>{let{__scopeSlider:c,onSlideStart:d,onSlideMove:e,onSlideEnd:g,onHomeKeyDown:h,onEndKeyDown:i,onStepKeyDown:j,...k}=a,l=z(s,c);return(0,o.jsx)(m.sG.span,{...k,ref:b,onKeyDown:(0,f.m)(a.onKeyDown,a=>{"Home"===a.key?(h(a),a.preventDefault()):"End"===a.key?(i(a),a.preventDefault()):p.concat(q).includes(a.key)&&(j(a),a.preventDefault())}),onPointerDown:(0,f.m)(a.onPointerDown,a=>{let b=a.target;b.setPointerCapture(a.pointerId),a.preventDefault(),l.thumbs.has(b)?b.focus():d(a)}),onPointerMove:(0,f.m)(a.onPointerMove,a=>{a.target.hasPointerCapture(a.pointerId)&&e(a)}),onPointerUp:(0,f.m)(a.onPointerUp,a=>{let b=a.target;b.hasPointerCapture(a.pointerId)&&(b.releasePointerCapture(a.pointerId),g(a))})})}),G="SliderTrack",H=d.forwardRef((a,b)=>{let{__scopeSlider:c,...d}=a,e=z(G,c);return(0,o.jsx)(m.sG.span,{"data-disabled":e.disabled?"":void 0,"data-orientation":e.orientation,...d,ref:b})});H.displayName=G;var I="SliderRange",J=d.forwardRef((a,b)=>{let{__scopeSlider:c,...e}=a,f=z(I,c),h=C(I,c),i=d.useRef(null),j=(0,g.s)(b,i),k=f.values.length,l=f.values.map(a=>O(a,f.min,f.max)),n=k>1?Math.min(...l):0,p=100-Math.max(...l);return(0,o.jsx)(m.sG.span,{"data-orientation":f.orientation,"data-disabled":f.disabled?"":void 0,...e,ref:j,style:{...a.style,[h.startEdge]:n+"%",[h.endEdge]:p+"%"}})});J.displayName=I;var K="SliderThumb",L=d.forwardRef((a,b)=>{let c=u(a.__scopeSlider),[e,f]=d.useState(null),h=(0,g.s)(b,a=>f(a)),i=d.useMemo(()=>e?c().findIndex(a=>a.ref.current===e):-1,[c,e]);return(0,o.jsx)(M,{...a,ref:h,index:i})}),M=d.forwardRef((a,b)=>{let{__scopeSlider:c,index:e,name:h,...i}=a,j=z(K,c),k=C(K,c),[n,p]=d.useState(null),q=(0,g.s)(b,a=>p(a)),r=!n||j.form||!!n.closest("form"),s=(0,l.X)(n),u=j.values[e],v=void 0===u?0:O(u,j.min,j.max),w=function(a,b){return b>2?`Value ${a+1} of ${b}`:2===b?["Minimum","Maximum"][a]:void 0}(e,j.values.length),x=s?.[k.size],y=x?function(a,b,c){let d=a/2,e=P([0,50],[0,d]);return(d-e(b)*c)*c}(x,v,k.direction):0;return d.useEffect(()=>{if(n)return j.thumbs.add(n),()=>{j.thumbs.delete(n)}},[n,j.thumbs]),(0,o.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[k.startEdge]:`calc(${v}% + ${y}px)`},children:[(0,o.jsx)(t.ItemSlot,{scope:a.__scopeSlider,children:(0,o.jsx)(m.sG.span,{role:"slider","aria-label":a["aria-label"]||w,"aria-valuemin":j.min,"aria-valuenow":u,"aria-valuemax":j.max,"aria-orientation":j.orientation,"data-orientation":j.orientation,"data-disabled":j.disabled?"":void 0,tabIndex:j.disabled?void 0:0,...i,ref:q,style:void 0===u?{display:"none"}:a.style,onFocus:(0,f.m)(a.onFocus,()=>{j.valueIndexToChangeRef.current=e})})}),r&&(0,o.jsx)(N,{name:h??(j.name?j.name+(j.values.length>1?"[]":""):void 0),form:j.form,value:u},e)]})});L.displayName=K;var N=d.forwardRef(({__scopeSlider:a,value:b,...c},e)=>{let f=d.useRef(null),h=(0,g.s)(f,e),i=(0,k.Z)(b);return d.useEffect(()=>{let a=f.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(i!==b&&c){let d=new Event("input",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[i,b]),(0,o.jsx)(m.sG.input,{style:{display:"none"},...c,ref:h,defaultValue:b})});function O(a,b,c){return(0,e.q)(100/(c-b)*(a-b),[0,100])}function P(a,b){return c=>{if(a[0]===a[1]||b[0]===b[1])return b[0];let d=(b[1]-b[0])/(a[1]-a[0]);return b[0]+d*(c-a[0])}}N.displayName="RadioBubbleInput";var Q=A,R=H,S=J,T=L},94199:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])}};
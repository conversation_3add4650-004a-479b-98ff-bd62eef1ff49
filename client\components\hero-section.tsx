"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SearchBar, SearchFilters } from "./search-bar"
import { 
  MapPin, 
  Star, 
  Users, 
  Shield, 
  Zap,
  TrendingUp,
  CheckCircle
} from "lucide-react"

interface HeroSectionProps {
  onSearch: (query: string, filters: SearchFilters) => void
}

const stats = [
  {
    icon: Users,
    value: "10,000+",
    label: "Pengguna Aktif"
  },
  {
    icon: MapPin,
    value: "500+",
    label: "Kost Terdaftar"
  },
  {
    icon: Star,
    value: "4.8",
    label: "Rating Rata-rata"
  },
  {
    icon: Shield,
    value: "100%",
    label: "Terverifikasi"
  }
]

const features = [
  {
    icon: Zap,
    title: "Preview Dinamis",
    description: "Lihat detail kost dengan preview interaktif dan carousel gambar"
  },
  {
    icon: TrendingUp,
    title: "Perbandingan Mudah",
    description: "Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail"
  },
  {
    icon: CheckCircle,
    title: "Terverifikasi",
    description: "Semua kost telah diverifikasi untuk memastikan kualitas dan keamanan"
  }
]

const popularLocations = [
  "Jakarta Selatan",
  "Bandung",
  "Yogyakarta", 
  "Surabaya",
  "Malang",
  "Semarang"
]

export function HeroSection({ onSearch }: HeroSectionProps) {
  const handleLocationSearch = (location: string) => {
    const filters: SearchFilters = {
      location,
      type: "semua",
      priceRange: [500000, 5000000],
      facilities: [],
      sortBy: "relevance"
    }
    onSearch("", filters)
  }

  return (
    <section className="relative overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 hero-gradient opacity-90" />
      <div className="absolute inset-0 bg-black/20" />
      
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative container mx-auto px-4 py-20 lg:py-32">
        <div className="text-center space-y-8">
          {/* Main Heading */}
          <div className="space-y-4">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
              🏠 Platform Pencarian Kost Terdepan
            </Badge>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
              Temukan Kost
              <br />
              <span className="text-yellow-300">Impian Anda</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Platform inovatif dengan preview dinamis dan fitur perbandingan interaktif 
              untuk membantu Anda menemukan tempat tinggal yang sempurna.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-4xl mx-auto">
            <SearchBar 
              onSearch={onSearch}
              placeholder="Cari berdasarkan lokasi, nama kost, atau fasilitas..."
              className="bg-white/95 backdrop-blur-sm rounded-2xl p-2 shadow-2xl"
            />
          </div>

          {/* Popular Locations */}
          <div className="space-y-4">
            <p className="text-white/80 text-sm font-medium">Lokasi Populer:</p>
            <div className="flex flex-wrap justify-center gap-2">
              {popularLocations.map((location) => (
                <Button
                  key={location}
                  variant="outline"
                  size="sm"
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20 hover:text-white transition-all duration-300"
                  onClick={() => handleLocationSearch(location)}
                >
                  <MapPin className="h-3 w-3 mr-1" />
                  {location}
                </Button>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto pt-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center space-y-2">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mb-2">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-white">
                  {stat.value}
                </div>
                <div className="text-sm text-white/80">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="relative bg-white/5 backdrop-blur-sm border-t border-white/10">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Mengapa Memilih KostHub?
            </h2>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Fitur-fitur inovatif yang memudahkan pencarian kost impian Anda
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="text-center space-y-4 group">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 group-hover:bg-white/20 transition-colors duration-300">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white">
                  {feature.title}
                </h3>
                <p className="text-white/80 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          {/* CTA */}
          <div className="text-center mt-12">
            <Button 
              size="lg" 
              className="bg-white text-primary hover:bg-white/90 font-semibold px-8 py-3 text-lg"
              onClick={() => {
                document.getElementById('kost-listings')?.scrollIntoView({ 
                  behavior: 'smooth' 
                })
              }}
            >
              Jelajahi Kost Sekarang
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

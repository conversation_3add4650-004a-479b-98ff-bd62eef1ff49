(()=>{var a={};a.id=475,a.ids=[475],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21353:()=>{},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30401:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{resolveManifest:function(){return g},resolveRobots:function(){return e},resolveRouteData:function(){return h},resolveSitemap:function(){return f}});let d=c(64015);function e(a){let b="";for(let c of Array.isArray(a.rules)?a.rules:[a.rules]){for(let a of(0,d.resolveArray)(c.userAgent||["*"]))b+=`User-Agent: ${a}
`;if(c.allow)for(let a of(0,d.resolveArray)(c.allow))b+=`Allow: ${a}
`;if(c.disallow)for(let a of(0,d.resolveArray)(c.disallow))b+=`Disallow: ${a}
`;c.crawlDelay&&(b+=`Crawl-delay: ${c.crawlDelay}
`),b+="\n"}return a.host&&(b+=`Host: ${a.host}
`),a.sitemap&&(0,d.resolveArray)(a.sitemap).forEach(a=>{b+=`Sitemap: ${a}
`}),b}function f(a){let b=a.some(a=>Object.keys(a.alternates??{}).length>0),c=a.some(a=>{var b;return!!(null==(b=a.images)?void 0:b.length)}),d=a.some(a=>{var b;return!!(null==(b=a.videos)?void 0:b.length)}),e="";for(let i of(e+='<?xml version="1.0" encoding="UTF-8"?>\n',e+='<urlset xmlns="https://www.sitemaps.org/schemas/sitemap/0.9"',c&&(e+=' xmlns:image="https://www.google.com/schemas/sitemap-image/1.1"'),d&&(e+=' xmlns:video="https://www.google.com/schemas/sitemap-video/1.1"'),b?e+=' xmlns:xhtml="https://www.w3.org/1999/xhtml">\n':e+=">\n",a)){var f,g,h;e+="<url>\n",e+=`<loc>${i.url}</loc>
`;let a=null==(f=i.alternates)?void 0:f.languages;if(a&&Object.keys(a).length)for(let b in a)e+=`<xhtml:link rel="alternate" hreflang="${b}" href="${a[b]}" />
`;if(null==(g=i.images)?void 0:g.length)for(let a of i.images)e+=`<image:image>
<image:loc>${a}</image:loc>
</image:image>
`;if(null==(h=i.videos)?void 0:h.length)for(let a of i.videos)e+=["<video:video>",`<video:title>${a.title}</video:title>`,`<video:thumbnail_loc>${a.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${a.description}</video:description>`,a.content_loc&&`<video:content_loc>${a.content_loc}</video:content_loc>`,a.player_loc&&`<video:player_loc>${a.player_loc}</video:player_loc>`,a.duration&&`<video:duration>${a.duration}</video:duration>`,a.view_count&&`<video:view_count>${a.view_count}</video:view_count>`,a.tag&&`<video:tag>${a.tag}</video:tag>`,a.rating&&`<video:rating>${a.rating}</video:rating>`,a.expiration_date&&`<video:expiration_date>${a.expiration_date}</video:expiration_date>`,a.publication_date&&`<video:publication_date>${a.publication_date}</video:publication_date>`,a.family_friendly&&`<video:family_friendly>${a.family_friendly}</video:family_friendly>`,a.requires_subscription&&`<video:requires_subscription>${a.requires_subscription}</video:requires_subscription>`,a.live&&`<video:live>${a.live}</video:live>`,a.restriction&&`<video:restriction relationship="${a.restriction.relationship}">${a.restriction.content}</video:restriction>`,a.platform&&`<video:platform relationship="${a.platform.relationship}">${a.platform.content}</video:platform>`,a.uploader&&`<video:uploader${a.uploader.info&&` info="${a.uploader.info}"`}>${a.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(i.lastModified){let a=i.lastModified instanceof Date?i.lastModified.toISOString():i.lastModified;e+=`<lastmod>${a}</lastmod>
`}i.changeFrequency&&(e+=`<changefreq>${i.changeFrequency}</changefreq>
`),"number"==typeof i.priority&&(e+=`<priority>${i.priority}</priority>
`),e+="</url>\n"}return e+"</urlset>\n"}function g(a){return JSON.stringify(a)}function h(a,b){return"robots"===b?e(a):"sitemap"===b?f(a):"manifest"===b?g(a):""}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64015:(a,b)=>{"use strict";function c(a){return Array.isArray(a)?a:[a]}function d(a){if(null!=a)return c(a)}function e(a){let b;if("string"==typeof a)try{b=(a=new URL(a)).origin}catch{}return b}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getOrigin:function(){return e},resolveArray:function(){return c},resolveAsArrayOrUndefined:function(){return d}})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86505:()=>{},95039:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>F,patchFetch:()=>E,routeModule:()=>A,serverHooks:()=>D,workAsyncStorage:()=>B,workUnitAsyncStorage:()=>C});var d={};c.r(d),c.d(d,{default:()=>w});var e={};c.r(e),c.d(e,{GET:()=>z});var f=c(6201),g=c(99014),h=c(78525),i=c(67309),j=c(39543),k=c(261),l=c(11261),m=c(13139),n=c(43349),o=c(25590),p=c(19032),q=c(83522),r=c(83424),s=c(48249),t=c(86439),u=c(88055),v=c(71672);function w(){let a="https://kosthub.com";return[{url:a,lastModified:new Date,changeFrequency:"daily",priority:1},{url:`${a}/listings`,lastModified:new Date,changeFrequency:"hourly",priority:.9},{url:`${a}/about`,lastModified:new Date,changeFrequency:"monthly",priority:.7},{url:`${a}/contact`,lastModified:new Date,changeFrequency:"monthly",priority:.6}]}var x=c(30401);let y={...d}.default;if("function"!=typeof y)throw Error('Default export is missing in "D:\\Vicky\\project baru\\kost\\client\\app\\sitemap.ts"');async function z(a,b){let{__metadata_id__:c,...d}=await b.params||{},e=!!c&&c.endsWith(".xml");if(c&&!e)return new v.NextResponse("Not Found",{status:404});let f=c&&e?c.slice(0,-4):void 0,g=await y({id:f}),h=(0,x.resolveRouteData)(g,"sitemap");return new v.NextResponse(h,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let A=new f.AppRouteRouteModule({definition:{kind:g.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},distDir:".next",projectDir:"",resolvedPagePath:"next-metadata-route-loader?filePath=D%3A%5CVicky%5Cproject%20baru%5Ckost%5Cclient%5Capp%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:e}),{workAsyncStorage:B,workUnitAsyncStorage:C,serverHooks:D}=A;function E(){return(0,h.patchFetch)({workAsyncStorage:B,workUnitAsyncStorage:C})}async function F(a,b,c){var d;let e="/sitemap.xml/route";"/index"===e&&(e="/");let f=await A.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:h,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=f,E=(0,k.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new t.NoFallbackError}let G=null;!F||A.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===A.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,j.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,i.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>A.onRequestError(a,b,d,z)},sharedContext:{buildId:h}},N=new l.NodeNextRequest(a),O=new l.NodeNextResponse(b),P=m.NextRequestAdapter.fromNodeNextRequest(N,(0,m.signalFromNodeResponse)(b));try{let d=async c=>A.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==n.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),f=async f=>{var h,j;let k=async({previousCacheEntry:g})=>{try{if(!(0,i.getRequestMeta)(a,"minimalMode")&&B&&C&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=M.renderOpts.fetchMetrics;let h=M.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,p.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,q.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[s.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=s.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=s.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:u.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await A.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,o.c)({isRevalidate:I,isOnDemandRevalidate:B})},z),b}},l=await A.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:g.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(h=l.value)?void 0:h.kind)!==u.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,q.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,i.getRequestMeta)(a,"minimalMode")&&F||m.delete(s.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,r.getCacheControlHeader)(l.cacheControl)),await (0,p.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await f(L):await K.withPropagatedContext(a.headers,()=>K.trace(n.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},f))}catch(b){if(L||await A.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,o.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,p.I)(N,O,new Response(null,{status:500})),null}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[795,253],()=>b(b.s=95039));module.exports=c})();
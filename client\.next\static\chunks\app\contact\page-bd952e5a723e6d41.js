(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{298:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>o});var s=t(2273);t(5461);var r=t(1415);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",a),...t})}},667:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(865).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},1415:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var s=t(9898),r=t(5422);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},2521:(e,a,t)=>{"use strict";t.d(a,{E:()=>d});var s=t(2273);t(5461);var r=t(3330),i=t(8092),n=t(1415);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:t,asChild:i=!1,...d}=e,o=i?r.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),a),...d})}},2785:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>N});var s=t(2273),r=t(5461),i=t(2521),n=t(7320),l=t(298),d=t(7444),o=t(3713),c=r.forwardRef((e,a)=>(0,s.jsx)(o.sG.label,{...e,ref:a,onMouseDown:a=>{var t;a.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));c.displayName="Label";var u=t(1415);function m(e){let{className:a,...t}=e;return(0,s.jsx)(c,{"data-slot":"label",className:(0,u.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,u.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}var p=t(8064),x=t(8684),h=t(4529),v=t(1101),b=t(667),f=t(5557),j=t(3565);let k=(0,t(865).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),y=[{icon:x.A,title:"Email",value:"<EMAIL>",description:"Kirim email untuk pertanyaan umum"},{icon:h.A,title:"Telepon",value:"+62 21 1234 5678",description:"Hubungi kami di jam kerja"},{icon:v.A,title:"Alamat",value:"Jakarta, Indonesia",description:"Kantor pusat kami"},{icon:b.A,title:"Jam Operasional",value:"Senin - Jumat, 09:00 - 18:00",description:"Waktu layanan customer service"}],w=[{question:"Bagaimana cara mencari kost di KostHub?",answer:"Anda dapat menggunakan fitur pencarian di halaman utama dengan memasukkan lokasi, budget, atau fasilitas yang diinginkan."},{question:"Apakah semua kost sudah terverifikasi?",answer:"Ya, semua kost yang terdaftar di KostHub telah melalui proses verifikasi untuk memastikan kualitas dan keamanan."},{question:"Bagaimana cara menggunakan fitur perbandingan?",answer:"Klik tombol 'Bandingkan' pada kost yang ingin dibandingkan, lalu klik tombol floating 'Bandingkan' untuk melihat perbandingan detail."},{question:"Apakah ada biaya untuk menggunakan platform ini?",answer:"Tidak, KostHub gratis untuk digunakan oleh pencari kost. Kami tidak mengenakan biaya apapun."}];function N(){let[e,a]=(0,r.useState)({name:"",email:"",subject:"",category:"",message:""}),[t,o]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),h=async e=>{e.preventDefault(),o(!0),await new Promise(e=>setTimeout(e,2e3)),o(!1),u(!0),setTimeout(()=>{u(!1),a({name:"",email:"",subject:"",category:"",message:""})},3e3)},v=(e,t)=>{a(a=>({...a,[e]:t}))};return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("section",{className:"py-20 bg-gradient-to-br from-primary/10 via-background to-accent/10",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsxs)(i.E,{variant:"outline",className:"mb-6",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Hubungi Kami"]}),(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:["Ada Pertanyaan?",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"text-primary",children:"Kami Siap Membantu"})]}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:"Tim customer service kami siap membantu Anda 24/7. Jangan ragu untuk menghubungi kami jika ada pertanyaan atau butuh bantuan dalam mencari kost impian."})]})}),(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16",children:y.map((e,a)=>(0,s.jsxs)(l.Zp,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:[(0,s.jsxs)(l.aR,{className:"pb-4",children:[(0,s.jsx)(e.icon,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,s.jsx)(l.ZB,{className:"text-xl",children:e.title})]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("p",{className:"font-semibold mb-2",children:e.value}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description})]})]},a))}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Kirim Pesan"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-8",children:"Isi form di bawah ini dan kami akan merespons dalam 24 jam."}),(0,s.jsx)(l.Zp,{className:"p-6",children:c?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(j.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Pesan Terkirim!"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Terima kasih atas pesan Anda. Tim kami akan segera merespons."})]}):(0,s.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"name",children:"Nama Lengkap"}),(0,s.jsx)(d.p,{id:"name",value:e.name,onChange:e=>v("name",e.target.value),placeholder:"Masukkan nama lengkap",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"email",children:"Email"}),(0,s.jsx)(d.p,{id:"email",type:"email",value:e.email,onChange:e=>v("email",e.target.value),placeholder:"<EMAIL>",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"category",children:"Kategori"}),(0,s.jsxs)(p.l6,{value:e.category,onValueChange:e=>v("category",e),children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{placeholder:"Pilih kategori pertanyaan"})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"general",children:"Pertanyaan Umum"}),(0,s.jsx)(p.eb,{value:"technical",children:"Masalah Teknis"}),(0,s.jsx)(p.eb,{value:"partnership",children:"Kerjasama"}),(0,s.jsx)(p.eb,{value:"complaint",children:"Keluhan"}),(0,s.jsx)(p.eb,{value:"suggestion",children:"Saran"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"subject",children:"Subjek"}),(0,s.jsx)(d.p,{id:"subject",value:e.subject,onChange:e=>v("subject",e.target.value),placeholder:"Subjek pesan",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"message",children:"Pesan"}),(0,s.jsx)(g,{id:"message",value:e.message,onChange:e=>v("message",e.target.value),placeholder:"Tulis pesan Anda di sini...",rows:6,required:!0})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full",size:"lg",disabled:t,children:t?"Mengirim...":(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k,{className:"h-4 w-4 mr-2"}),"Kirim Pesan"]})})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"FAQ"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-8",children:"Pertanyaan yang sering diajukan oleh pengguna kami."}),(0,s.jsx)("div",{className:"space-y-4",children:w.map((e,a)=>(0,s.jsxs)(l.Zp,{className:"p-6",children:[(0,s.jsx)("h3",{className:"font-semibold mb-3",children:e.question}),(0,s.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e.answer})]},a))}),(0,s.jsxs)(l.Zp,{className:"p-6 mt-6 bg-primary/5 border-primary/20",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Tidak menemukan jawaban?"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Jika pertanyaan Anda tidak terjawab di FAQ, jangan ragu untuk menghubungi kami langsung."}),(0,s.jsxs)(n.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Email Kami"]})]})]})]})]})})]})}},3116:(e,a,t)=>{Promise.resolve().then(t.bind(t,2785))},3565:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(865).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4529:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(865).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},5557:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(865).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},7320:(e,a,t)=>{"use strict";t.d(a,{$:()=>d});var s=t(2273);t(5461);var r=t(3330),i=t(8092),n=t(1415);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:t,size:i,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:a})),...o})}},7444:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(2273);t(5461);var r=t(1415);function i(e){let{className:a,type:t,...i}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}},8064:(e,a,t)=>{"use strict";t.d(a,{bq:()=>u,eb:()=>g,gC:()=>m,l6:()=>o,yv:()=>c});var s=t(2273);t(5461);var r=t(6310),i=t(3589),n=t(4137),l=t(4344),d=t(1415);function o(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function u(e){let{className:a,size:t="default",children:n,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:a,children:t,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(x,{})]})})}function g(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function p(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function x(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},8684:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(865).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{e.O(0,[335,482,739,970,804,358],()=>e(e.s=3116)),_N_E=e.O()}]);
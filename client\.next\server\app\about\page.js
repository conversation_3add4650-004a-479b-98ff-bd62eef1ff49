(()=>{var a={};a.id=220,a.ids=[220],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5772:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e,metadata:()=>d});let d={title:"Tentang Kami - KostHub",description:"Pelajari lebih lanjut tentang KostHub, platform pencarian kost inovatif yang mengubah cara Anda menemukan tempat tinggal ideal di Indonesia.",keywords:["tentang kosthub","visi misi","tim kosthub","sejarah perusa<PERSON>an"],openGraph:{title:"Tentang Kami - KostHub",description:"Platform pencarian kost inovatif yang mengubah cara Anda menemukan tempat tinggal ideal.",images:[c(62426).Y$.og.about]}};function e({children:a}){return a}},10529:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(71737).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21353:()=>{},25763:(a,b,c)=>{Promise.resolve().then(c.bind(c,38883))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},38883:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(58999).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Vicky\\\\project baru\\\\kost\\\\client\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\app\\about\\page.tsx","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},56357:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(21157);c(43616);var e=c(84726),f=c(59542),g=c(89369);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69814:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(71737).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86505:()=>{},91419:(a,b,c)=>{Promise.resolve().then(c.bind(c,92464))},92464:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(21157),e=c(70696),f=c(56357),g=c(90574),h=c(80832),i=c(52945),j=c(8932),k=c(69814),l=c(88358),m=c(68307),n=c(10529),o=c(38163),p=c(71737);let q=(0,p.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var r=c(49679);let s=(0,p.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var t=c(81986),u=c(96220);let v=[{icon:j.A,title:"Preview Dinamis",description:"Lihat detail kost dengan preview interaktif, carousel gambar, dan informasi lengkap sebelum memutuskan."},{icon:k.A,title:"Perbandingan Mudah",description:"Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail untuk membantu keputusan Anda."},{icon:l.A,title:"Terverifikasi",description:"Semua kost telah diverifikasi untuk memastikan kualitas, keamanan, dan kesesuaian informasi."},{icon:m.A,title:"Komunitas Terpercaya",description:"Bergabung dengan ribuan pengguna yang telah menemukan tempat tinggal ideal melalui platform kami."}],w=[{icon:m.A,value:"10,000+",label:"Pengguna Aktif"},{icon:n.A,value:"500+",label:"Kost Terdaftar"},{icon:o.A,value:"4.8",label:"Rating Rata-rata"},{icon:q,value:"98%",label:"Tingkat Kepuasan"}],x=[{name:"Ahmad Rizki",role:"CEO & Founder",description:"Berpengalaman 10+ tahun di industri properti dan teknologi.",avatar:u.Y$.avatars.male4},{name:"Sarah Putri",role:"CTO",description:"Expert dalam pengembangan platform digital dan user experience.",avatar:u.Y$.avatars.female4},{name:"Dina Maharani",role:"Head of Operations",description:"Spesialis dalam operasional bisnis dan customer relationship.",avatar:u.Y$.avatars.female5}];function y(){return(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)("section",{className:"py-20 bg-gradient-to-br from-primary/10 via-background to-accent/10",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,d.jsxs)(f.E,{variant:"outline",className:"mb-6",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"Tentang KostHub"]}),(0,d.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:["Revolusi Pencarian Kost",(0,d.jsx)("br",{}),(0,d.jsx)("span",{className:"text-primary",children:"di Indonesia"})]}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:"KostHub hadir untuk mengubah cara Anda mencari dan menemukan kost impian. Dengan teknologi inovatif dan fitur-fitur canggih, kami membuat proses pencarian kost menjadi lebih mudah, cepat, dan menyenangkan."})]})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 max-w-6xl mx-auto",children:[(0,d.jsxs)(h.Zp,{className:"p-8",children:[(0,d.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,d.jsx)(s,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,d.jsx)(h.ZB,{className:"text-2xl",children:"Misi Kami"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground leading-relaxed text-center",children:"Menyediakan platform pencarian kost yang inovatif, terpercaya, dan mudah digunakan untuk membantu setiap orang menemukan tempat tinggal yang sesuai dengan kebutuhan dan budget mereka."})})]}),(0,d.jsxs)(h.Zp,{className:"p-8",children:[(0,d.jsxs)(h.aR,{className:"text-center pb-6",children:[(0,d.jsx)(t.A,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,d.jsx)(h.ZB,{className:"text-2xl",children:"Visi Kami"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground leading-relaxed text-center",children:"Menjadi platform pencarian kost terdepan di Indonesia yang menghubungkan pencari kost dengan penyedia kost melalui teknologi yang user-friendly dan fitur-fitur inovatif."})})]})]})})}),(0,d.jsx)(i.w,{}),(0,d.jsx)("section",{className:"py-16 bg-muted/30",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Mengapa Memilih KostHub?"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Fitur-fitur inovatif yang membedakan kami dari platform pencarian kost lainnya"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:v.map((a,b)=>(0,d.jsxs)(h.Zp,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:[(0,d.jsxs)(h.aR,{className:"pb-4",children:[(0,d.jsx)(a.icon,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,d.jsx)(h.ZB,{className:"text-xl",children:a.title})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:a.description})})]},b))})]})}),(0,d.jsx)(i.w,{}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Pencapaian Kami"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg",children:"Angka-angka yang menunjukkan kepercayaan pengguna terhadap KostHub"})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:w.map((a,b)=>(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-bold text-primary mb-2",children:a.value}),(0,d.jsx)("div",{className:"text-muted-foreground",children:a.label})]},b))})]})}),(0,d.jsx)(i.w,{}),(0,d.jsx)("section",{className:"py-16 bg-muted/30",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Tim Kami"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Orang-orang berpengalaman yang berdedikasi untuk memberikan layanan terbaik"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:x.map((a,b)=>(0,d.jsxs)(h.Zp,{className:"text-center p-6",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)("div",{className:"relative w-20 h-20 rounded-full mx-auto mb-4 overflow-hidden",children:(0,d.jsx)(e.default,{src:a.avatar,alt:`${a.name} avatar`,fill:!0,className:"object-cover",sizes:"80px"})}),(0,d.jsx)(h.ZB,{className:"text-xl",children:a.name}),(0,d.jsx)("p",{className:"text-primary font-medium",children:a.role})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground",children:a.description})})]},b))})]})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto space-y-6",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold",children:"Siap Bergabung dengan KostHub?"}),(0,d.jsx)("p",{className:"text-muted-foreground text-lg",children:"Mulai pencarian kost impian Anda hari ini dan rasakan pengalaman yang berbeda"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(g.$,{size:"lg",className:"px-8",children:"Mulai Pencarian"}),(0,d.jsx)(g.$,{size:"lg",variant:"outline",className:"px-8",children:"Hubungi Kami"})]})]})})})]})}},96220:(a,b,c)=>{"use strict";c.d(b,{Et:()=>g,Y$:()=>d,wf:()=>e});let d={kost:{room1:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center",room2:"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center",room3:"https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop&crop=center",room4:"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center",room5:"https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=800&h=600&fit=crop&crop=center",room6:"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center",interior1:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop&crop=center",interior2:"https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center",interior3:"https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop&crop=center"},avatars:{male1:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center",female1:"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=100&h=100&fit=crop&crop=center",male2:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center",female2:"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=center",male3:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=center",female3:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=center",male4:"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=center",female4:"https://images.unsplash.com/photo-1607746882042-944635dfe10e?w=100&h=100&fit=crop&crop=center",male5:"https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=100&h=100&fit=crop&crop=center",female5:"https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=100&h=100&fit=crop&crop=center"},og:{main:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center",listings:"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center",about:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1200&h=630&fit=crop&crop=center",contact:"https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1200&h=630&fit=crop&crop=center"},buildings:{jakarta:"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center",bandung:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center",yogya:"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center",surabaya:"https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&h=600&fit=crop&crop=center"},facilities:{wifi:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",parking:"https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=400&h=300&fit=crop&crop=center",kitchen:"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center",security:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",laundry:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center"}},e=d.kost.room1;d.avatars.male1;let f={kost:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center",og:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center"},g=(a,b="kost")=>a&&a.includes("images.unsplash.com")?a:f[b];d.kost.room1,d.kost.room2,d.kost.room3},97238:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(52865),e=c(99014),f=c(25590),g=c(39543),h=c(67309),i=c(43349),j=c(70108),k=c(11261),l=c(89527),m=c(34275),n=c(12292),o=c(64738),p=c(55935),q=c(261),r=c(32710),s=c(68923),t=c(26713),u=c(88055),v=c(54084),w=c(28312),x=c(48249),y=c(67455),z=c(6085),A=c(86439),B=c(27419),C=c.n(B),D=c(5031),E=c(76950),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,38883)),"D:\\Vicky\\project baru\\kost\\client\\app\\about\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,5772)),"D:\\Vicky\\project baru\\kost\\client\\app\\about\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,18729))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,9954)),"D:\\Vicky\\project baru\\kost\\client\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,27419,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,56211,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90998,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,99233,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,18729))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Vicky\\project baru\\kost\\client\\app\\about\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/about/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[795,384,347,470],()=>b(b.s=97238));module.exports=c})();
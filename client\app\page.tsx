"use client"

import { useState, lazy, Suspense } from "react"
import { HeroSec<PERSON> } from "@/components/hero-section"
import { <PERSON>st<PERSON><PERSON>, KostData } from "@/components/kost-card"
import { DialogSkeleton } from "@/components/loading"
import { UNSPLASH_IMAGES } from "@/lib/images"
import Image from "next/image"

// Lazy load heavy components
const KostPreviewDialog = lazy(() => import("@/components/kost-preview-dialog").then(mod => ({ default: mod.KostPreviewDialog })))
const ComparisonDialog = lazy(() => import("@/components/comparison-dialog").then(mod => ({ default: mod.ComparisonDialog })))
import { SearchFilters } from "@/components/search-bar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  TrendingUp,
  Star,
  MapPin,
  Users,
  ArrowRight,
  Heart,
  Shield
} from "lucide-react"

// Mock data untuk featured kost
const featuredKosts: KostData[] = [
  {
    id: "1",
    title: "Kost Melati Residence",
    location: "Kemang, Jakarta Selatan",
    price: 2500000,
    rating: 4.8,
    reviewCount: 124,
    images: [
      UNSPLASH_IMAGES.kost.room1,
      UNSPLASH_IMAGES.kost.interior1,
      UNSPLASH_IMAGES.kost.interior2
    ],
    facilities: ["WiFi", "Parkir", "Dapur", "Listrik", "Air", "Keamanan"],
    type: "putri",
    available: 3,
    description: "Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.",
    isWishlisted: false
  },
  {
    id: "2",
    title: "Griya Mahasiswa Bandung",
    location: "Dago, Bandung",
    price: 1800000,
    rating: 4.6,
    reviewCount: 89,
    images: [
      UNSPLASH_IMAGES.kost.room2,
      UNSPLASH_IMAGES.kost.interior3
    ],
    facilities: ["WiFi", "Dapur", "Listrik", "Air", "Ruang Tamu"],
    type: "putra",
    available: 5,
    description: "Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.",
    isWishlisted: true
  },
  {
    id: "3",
    title: "Kost Harmoni Yogya",
    location: "Malioboro, Yogyakarta",
    price: 1500000,
    rating: 4.7,
    reviewCount: 156,
    images: [UNSPLASH_IMAGES.kost.room3],
    facilities: ["WiFi", "Parkir", "Listrik", "Air", "Keamanan", "AC"],
    type: "campur",
    available: 2,
    description: "Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.",
    isWishlisted: false
  }
]

const testimonials = [
  {
    name: "Sarah Putri",
    location: "Jakarta",
    rating: 5,
    comment: "Platform yang sangat membantu! Fitur perbandingan kostnya sangat berguna untuk memilih kost yang tepat.",
    avatar: UNSPLASH_IMAGES.avatars.female1
  },
  {
    name: "Ahmad Rizki",
    location: "Bandung",
    rating: 5,
    comment: "Preview dinamis membuat saya bisa melihat detail kost dengan jelas sebelum memutuskan. Recommended!",
    avatar: UNSPLASH_IMAGES.avatars.male1
  },
  {
    name: "Dina Maharani",
    location: "Yogyakarta",
    rating: 4,
    comment: "Pencarian kost jadi lebih mudah dan cepat. Interface yang user-friendly dan informatif.",
    avatar: UNSPLASH_IMAGES.avatars.female2
  }
]

export default function Home() {
  const [selectedKost, setSelectedKost] = useState<KostData | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [comparisonKosts, setComparisonKosts] = useState<KostData[]>([])
  const [isComparisonOpen, setIsComparisonOpen] = useState(false)
  const [wishlistedKosts, setWishlistedKosts] = useState<string[]>(["2"])

  const handleSearch = (query: string, filters: SearchFilters) => {
    // TODO: Implement search functionality
    console.log("Search:", query, filters)
    // Navigate to listings page or filter results
  }

  const handlePreview = (kost: KostData) => {
    setSelectedKost(kost)
    setIsPreviewOpen(true)
  }

  const handleWishlist = (kostId: string) => {
    setWishlistedKosts(prev =>
      prev.includes(kostId)
        ? prev.filter(id => id !== kostId)
        : [...prev, kostId]
    )
  }

  const handleCompare = (kostId: string) => {
    const kost = featuredKosts.find(k => k.id === kostId)
    if (!kost) return

    setComparisonKosts(prev => {
      const isAlreadyComparing = prev.some(k => k.id === kostId)
      if (isAlreadyComparing) {
        return prev.filter(k => k.id !== kostId)
      } else if (prev.length < 3) {
        return [...prev, kost]
      } else {
        // Replace the first item if already at max
        return [kost, ...prev.slice(1)]
      }
    })
  }

  const removeFromComparison = (kostId: string) => {
    setComparisonKosts(prev => prev.filter(k => k.id !== kostId))
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection onSearch={handleSearch} />

      {/* Featured Kosts Section */}
      <section id="kost-listings" className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4">
              <TrendingUp className="h-4 w-4 mr-2" />
              Kost Terpopuler
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Kost Pilihan Terbaik
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Temukan kost berkualitas tinggi yang telah dipilih khusus untuk Anda
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {featuredKosts.map((kost) => (
              <KostCard
                key={kost.id}
                kost={{
                  ...kost,
                  isWishlisted: wishlistedKosts.includes(kost.id)
                }}
                onPreview={handlePreview}
                onWishlist={handleWishlist}
                onCompare={handleCompare}
                isComparing={comparisonKosts.some(k => k.id === kost.id)}
              />
            ))}
          </div>

          <div className="text-center">
            <Button size="lg" variant="outline">
              Lihat Semua Kost
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      <Separator />

      {/* Testimonials Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4">
              <Users className="h-4 w-4 mr-2" />
              Testimoni Pengguna
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Apa Kata Mereka?
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Pengalaman nyata dari pengguna KostHub yang telah menemukan kost impian mereka
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-card p-6 rounded-lg border">
                <div className="flex items-center gap-1 mb-4">
                  {Array.from({ length: testimonial.rating }).map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  "{testimonial.comment}"
                </p>
                <div className="flex items-center gap-3">
                  <div className="relative w-10 h-10 rounded-full overflow-hidden">
                    <Image
                      src={testimonial.avatar}
                      alt={`${testimonial.name} avatar`}
                      fill
                      className="object-cover"
                      sizes="40px"
                    />
                  </div>
                  <div>
                    <div className="font-medium">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {testimonial.location}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Separator />

      {/* CTA Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto space-y-6">
            <Badge variant="outline" className="mb-4">
              <Shield className="h-4 w-4 mr-2" />
              Bergabung Sekarang
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold">
              Siap Menemukan Kost Impian Anda?
            </h2>
            <p className="text-muted-foreground text-lg">
              Bergabunglah dengan ribuan pengguna yang telah menemukan tempat tinggal ideal mereka melalui KostHub
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8">
                <Heart className="h-4 w-4 mr-2" />
                Mulai Pencarian
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                Daftarkan Kost Anda
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Floating Button */}
      {comparisonKosts.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={() => setIsComparisonOpen(true)}
            className="rounded-full shadow-lg"
            size="lg"
          >
            Bandingkan ({comparisonKosts.length})
          </Button>
        </div>
      )}

      {/* Dialogs */}
      <Suspense fallback={<DialogSkeleton />}>
        <KostPreviewDialog
          kost={selectedKost}
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          onWishlist={handleWishlist}
          onCompare={handleCompare}
          isComparing={selectedKost ? comparisonKosts.some(k => k.id === selectedKost.id) : false}
        />

        <ComparisonDialog
          kosts={comparisonKosts}
          isOpen={isComparisonOpen}
          onClose={() => setIsComparisonOpen(false)}
          onRemoveFromComparison={removeFromComparison}
        />
      </Suspense>
    </div>
  )
}

"use strict";exports.id=888,exports.ids=[888],exports.modules={54606:(a,b,c)=>{c.d(b,{LM:()=>W,OK:()=>X,VM:()=>x,bL:()=>V,lr:()=>I});var d=c(43616),e=c(7957),f=c(17731),g=c(37784),h=c(84677),i=c(24452),j=c(78309),k=c(27841),l=c(21311),m=c(86984),n=c(21157),o="ScrollArea",[p,q]=(0,g.A)(o),[r,s]=p(o),t=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,type:f="hover",dir:g,scrollHideDelay:i=600,...k}=a,[l,m]=d.useState(null),[o,p]=d.useState(null),[q,s]=d.useState(null),[t,u]=d.useState(null),[v,w]=d.useState(null),[x,y]=d.useState(0),[z,A]=d.useState(0),[B,C]=d.useState(!1),[D,E]=d.useState(!1),F=(0,h.s)(b,a=>m(a)),G=(0,j.jH)(g);return(0,n.jsx)(r,{scope:c,type:f,dir:G,scrollHideDelay:i,scrollArea:l,viewport:o,onViewportChange:p,content:q,onContentChange:s,scrollbarX:t,onScrollbarXChange:u,scrollbarXEnabled:B,onScrollbarXEnabledChange:C,scrollbarY:v,onScrollbarYChange:w,scrollbarYEnabled:D,onScrollbarYEnabledChange:E,onCornerWidthChange:y,onCornerHeightChange:A,children:(0,n.jsx)(e.sG.div,{dir:G,...k,ref:F,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":z+"px",...a.style}})})});t.displayName=o;var u="ScrollAreaViewport",v=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,children:f,nonce:g,...i}=a,j=s(u,c),k=d.useRef(null),l=(0,h.s)(b,k,j.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:g}),(0,n.jsx)(e.sG.div,{"data-radix-scroll-area-viewport":"",...i,ref:l,style:{overflowX:j.scrollbarXEnabled?"scroll":"hidden",overflowY:j.scrollbarYEnabled?"scroll":"hidden",...a.style},children:(0,n.jsx)("div",{ref:j.onContentChange,style:{minWidth:"100%",display:"table"},children:f})})]})});v.displayName=u;var w="ScrollAreaScrollbar",x=d.forwardRef((a,b)=>{let{forceMount:c,...e}=a,f=s(w,a.__scopeScrollArea),{onScrollbarXEnabledChange:g,onScrollbarYEnabledChange:h}=f,i="horizontal"===a.orientation;return d.useEffect(()=>(i?g(!0):h(!0),()=>{i?g(!1):h(!1)}),[i,g,h]),"hover"===f.type?(0,n.jsx)(y,{...e,ref:b,forceMount:c}):"scroll"===f.type?(0,n.jsx)(z,{...e,ref:b,forceMount:c}):"auto"===f.type?(0,n.jsx)(A,{...e,ref:b,forceMount:c}):"always"===f.type?(0,n.jsx)(B,{...e,ref:b}):null});x.displayName=w;var y=d.forwardRef((a,b)=>{let{forceMount:c,...e}=a,g=s(w,a.__scopeScrollArea),[h,i]=d.useState(!1);return d.useEffect(()=>{let a=g.scrollArea,b=0;if(a){let c=()=>{window.clearTimeout(b),i(!0)},d=()=>{b=window.setTimeout(()=>i(!1),g.scrollHideDelay)};return a.addEventListener("pointerenter",c),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(b),a.removeEventListener("pointerenter",c),a.removeEventListener("pointerleave",d)}}},[g.scrollArea,g.scrollHideDelay]),(0,n.jsx)(f.C,{present:c||h,children:(0,n.jsx)(A,{"data-state":h?"visible":"hidden",...e,ref:b})})}),z=d.forwardRef((a,b)=>{var c;let{forceMount:e,...g}=a,h=s(w,a.__scopeScrollArea),i="horizontal"===a.orientation,j=T(()=>l("SCROLL_END"),100),[k,l]=(c={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},d.useReducer((a,b)=>c[a][b]??a,"hidden"));return d.useEffect(()=>{if("idle"===k){let a=window.setTimeout(()=>l("HIDE"),h.scrollHideDelay);return()=>window.clearTimeout(a)}},[k,h.scrollHideDelay,l]),d.useEffect(()=>{let a=h.viewport,b=i?"scrollLeft":"scrollTop";if(a){let c=a[b],d=()=>{let d=a[b];c!==d&&(l("SCROLL"),j()),c=d};return a.addEventListener("scroll",d),()=>a.removeEventListener("scroll",d)}},[h.viewport,i,l,j]),(0,n.jsx)(f.C,{present:e||"hidden"!==k,children:(0,n.jsx)(B,{"data-state":"hidden"===k?"hidden":"visible",...g,ref:b,onPointerEnter:(0,m.m)(a.onPointerEnter,()=>l("POINTER_ENTER")),onPointerLeave:(0,m.m)(a.onPointerLeave,()=>l("POINTER_LEAVE"))})})}),A=d.forwardRef((a,b)=>{let c=s(w,a.__scopeScrollArea),{forceMount:e,...g}=a,[h,i]=d.useState(!1),j="horizontal"===a.orientation,k=T(()=>{if(c.viewport){let a=c.viewport.offsetWidth<c.viewport.scrollWidth,b=c.viewport.offsetHeight<c.viewport.scrollHeight;i(j?a:b)}},10);return U(c.viewport,k),U(c.content,k),(0,n.jsx)(f.C,{present:e||h,children:(0,n.jsx)(B,{"data-state":h?"visible":"hidden",...g,ref:b})})}),B=d.forwardRef((a,b)=>{let{orientation:c="vertical",...e}=a,f=s(w,a.__scopeScrollArea),g=d.useRef(null),h=d.useRef(0),[i,j]=d.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),k=O(i.viewport,i.content),l={...e,sizes:i,onSizesChange:j,hasThumb:!!(k>0&&k<1),onThumbChange:a=>g.current=a,onThumbPointerUp:()=>h.current=0,onThumbPointerDown:a=>h.current=a};function m(a,b){return function(a,b,c,d="ltr"){let e=P(c),f=b||e/2,g=c.scrollbar.paddingStart+f,h=c.scrollbar.size-c.scrollbar.paddingEnd-(e-f),i=c.content-c.viewport;return R([g,h],"ltr"===d?[0,i]:[-1*i,0])(a)}(a,h.current,i,b)}return"horizontal"===c?(0,n.jsx)(C,{...l,ref:b,onThumbPositionChange:()=>{if(f.viewport&&g.current){let a=Q(f.viewport.scrollLeft,i,f.dir);g.current.style.transform=`translate3d(${a}px, 0, 0)`}},onWheelScroll:a=>{f.viewport&&(f.viewport.scrollLeft=a)},onDragScroll:a=>{f.viewport&&(f.viewport.scrollLeft=m(a,f.dir))}}):"vertical"===c?(0,n.jsx)(D,{...l,ref:b,onThumbPositionChange:()=>{if(f.viewport&&g.current){let a=Q(f.viewport.scrollTop,i);g.current.style.transform=`translate3d(0, ${a}px, 0)`}},onWheelScroll:a=>{f.viewport&&(f.viewport.scrollTop=a)},onDragScroll:a=>{f.viewport&&(f.viewport.scrollTop=m(a))}}):null}),C=d.forwardRef((a,b)=>{let{sizes:c,onSizesChange:e,...f}=a,g=s(w,a.__scopeScrollArea),[i,j]=d.useState(),k=d.useRef(null),l=(0,h.s)(b,k,g.onScrollbarXChange);return d.useEffect(()=>{k.current&&j(getComputedStyle(k.current))},[k]),(0,n.jsx)(G,{"data-orientation":"horizontal",...f,ref:l,sizes:c,style:{bottom:0,left:"rtl"===g.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===g.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":P(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.x),onDragScroll:b=>a.onDragScroll(b.x),onWheelScroll:(b,c)=>{if(g.viewport){let d=g.viewport.scrollLeft+b.deltaX;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{k.current&&g.viewport&&i&&e({content:g.viewport.scrollWidth,viewport:g.viewport.offsetWidth,scrollbar:{size:k.current.clientWidth,paddingStart:N(i.paddingLeft),paddingEnd:N(i.paddingRight)}})}})}),D=d.forwardRef((a,b)=>{let{sizes:c,onSizesChange:e,...f}=a,g=s(w,a.__scopeScrollArea),[i,j]=d.useState(),k=d.useRef(null),l=(0,h.s)(b,k,g.onScrollbarYChange);return d.useEffect(()=>{k.current&&j(getComputedStyle(k.current))},[k]),(0,n.jsx)(G,{"data-orientation":"vertical",...f,ref:l,sizes:c,style:{top:0,right:"ltr"===g.dir?0:void 0,left:"rtl"===g.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":P(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.y),onDragScroll:b=>a.onDragScroll(b.y),onWheelScroll:(b,c)=>{if(g.viewport){let d=g.viewport.scrollTop+b.deltaY;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{k.current&&g.viewport&&i&&e({content:g.viewport.scrollHeight,viewport:g.viewport.offsetHeight,scrollbar:{size:k.current.clientHeight,paddingStart:N(i.paddingTop),paddingEnd:N(i.paddingBottom)}})}})}),[E,F]=p(w),G=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,sizes:f,hasThumb:g,onThumbChange:j,onThumbPointerUp:k,onThumbPointerDown:l,onThumbPositionChange:o,onDragScroll:p,onWheelScroll:q,onResize:r,...t}=a,u=s(w,c),[v,x]=d.useState(null),y=(0,h.s)(b,a=>x(a)),z=d.useRef(null),A=d.useRef(""),B=u.viewport,C=f.content-f.viewport,D=(0,i.c)(q),F=(0,i.c)(o),G=T(r,10);function H(a){z.current&&p({x:a.clientX-z.current.left,y:a.clientY-z.current.top})}return d.useEffect(()=>{let a=a=>{let b=a.target;v?.contains(b)&&D(a,C)};return document.addEventListener("wheel",a,{passive:!1}),()=>document.removeEventListener("wheel",a,{passive:!1})},[B,v,C,D]),d.useEffect(F,[f,F]),U(v,G),U(u.content,G),(0,n.jsx)(E,{scope:c,scrollbar:v,hasThumb:g,onThumbChange:(0,i.c)(j),onThumbPointerUp:(0,i.c)(k),onThumbPositionChange:F,onThumbPointerDown:(0,i.c)(l),children:(0,n.jsx)(e.sG.div,{...t,ref:y,style:{position:"absolute",...t.style},onPointerDown:(0,m.m)(a.onPointerDown,a=>{0===a.button&&(a.target.setPointerCapture(a.pointerId),z.current=v.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",u.viewport&&(u.viewport.style.scrollBehavior="auto"),H(a))}),onPointerMove:(0,m.m)(a.onPointerMove,H),onPointerUp:(0,m.m)(a.onPointerUp,a=>{let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),document.body.style.webkitUserSelect=A.current,u.viewport&&(u.viewport.style.scrollBehavior=""),z.current=null})})})}),H="ScrollAreaThumb",I=d.forwardRef((a,b)=>{let{forceMount:c,...d}=a,e=F(H,a.__scopeScrollArea);return(0,n.jsx)(f.C,{present:c||e.hasThumb,children:(0,n.jsx)(J,{ref:b,...d})})}),J=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,style:f,...g}=a,i=s(H,c),j=F(H,c),{onThumbPositionChange:k}=j,l=(0,h.s)(b,a=>j.onThumbChange(a)),o=d.useRef(void 0),p=T(()=>{o.current&&(o.current(),o.current=void 0)},100);return d.useEffect(()=>{let a=i.viewport;if(a){let b=()=>{p(),o.current||(o.current=S(a,k),k())};return k(),a.addEventListener("scroll",b),()=>a.removeEventListener("scroll",b)}},[i.viewport,p,k]),(0,n.jsx)(e.sG.div,{"data-state":j.hasThumb?"visible":"hidden",...g,ref:l,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...f},onPointerDownCapture:(0,m.m)(a.onPointerDownCapture,a=>{let b=a.target.getBoundingClientRect(),c=a.clientX-b.left,d=a.clientY-b.top;j.onThumbPointerDown({x:c,y:d})}),onPointerUp:(0,m.m)(a.onPointerUp,j.onThumbPointerUp)})});I.displayName=H;var K="ScrollAreaCorner",L=d.forwardRef((a,b)=>{let c=s(K,a.__scopeScrollArea),d=!!(c.scrollbarX&&c.scrollbarY);return"scroll"!==c.type&&d?(0,n.jsx)(M,{...a,ref:b}):null});L.displayName=K;var M=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,...f}=a,g=s(K,c),[h,i]=d.useState(0),[j,k]=d.useState(0),l=!!(h&&j);return U(g.scrollbarX,()=>{let a=g.scrollbarX?.offsetHeight||0;g.onCornerHeightChange(a),k(a)}),U(g.scrollbarY,()=>{let a=g.scrollbarY?.offsetWidth||0;g.onCornerWidthChange(a),i(a)}),l?(0,n.jsx)(e.sG.div,{...f,ref:b,style:{width:h,height:j,position:"absolute",right:"ltr"===g.dir?0:void 0,left:"rtl"===g.dir?0:void 0,bottom:0,...a.style}}):null});function N(a){return a?parseInt(a,10):0}function O(a,b){let c=a/b;return isNaN(c)?0:c}function P(a){let b=O(a.viewport,a.content),c=a.scrollbar.paddingStart+a.scrollbar.paddingEnd;return Math.max((a.scrollbar.size-c)*b,18)}function Q(a,b,c="ltr"){let d=P(b),e=b.scrollbar.paddingStart+b.scrollbar.paddingEnd,f=b.scrollbar.size-e,g=b.content-b.viewport,h=(0,l.q)(a,"ltr"===c?[0,g]:[-1*g,0]);return R([0,g],[0,f-d])(h)}function R(a,b){return c=>{if(a[0]===a[1]||b[0]===b[1])return b[0];let d=(b[1]-b[0])/(a[1]-a[0]);return b[0]+d*(c-a[0])}}var S=(a,b=()=>{})=>{let c={left:a.scrollLeft,top:a.scrollTop},d=0;return!function e(){let f={left:a.scrollLeft,top:a.scrollTop},g=c.left!==f.left,h=c.top!==f.top;(g||h)&&b(),c=f,d=window.requestAnimationFrame(e)}(),()=>window.cancelAnimationFrame(d)};function T(a,b){let c=(0,i.c)(a),e=d.useRef(0);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),d.useCallback(()=>{window.clearTimeout(e.current),e.current=window.setTimeout(c,b)},[c,b])}function U(a,b){let c=(0,i.c)(b);(0,k.N)(()=>{let b=0;if(a){let d=new ResizeObserver(()=>{cancelAnimationFrame(b),b=window.requestAnimationFrame(c)});return d.observe(a),()=>{window.cancelAnimationFrame(b),d.unobserve(a)}}},[a,c])}var V=t,W=v,X=L},95763:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])}};
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[214],{87:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(865).A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},428:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(865).A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},1821:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(865).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},2685:(e,t,r)=>{r.d(t,{CC:()=>T,Q6:()=>V,bL:()=>O,zi:()=>X});var n=r(5461),o=r(8777),i=r(582),a=r(7239),l=r(4284),d=r(4976),s=r(2329),u=r(5749),c=r(9241),f=r(3713),p=r(7698),h=r(2273),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],w={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[g,x,b]=(0,p.N)(y),[S,A]=(0,l.A)(y,[b]),[M,k]=S(y),D=n.forwardRef((e,t)=>{let{name:r,min:a=0,max:l=100,step:s=1,orientation:u="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[a],value:w,onValueChange:y=()=>{},onValueCommit:x=()=>{},inverted:b=!1,form:S,...A}=e,k=n.useRef(new Set),D=n.useRef(0),R="horizontal"===u,[j=[],_]=(0,d.i)({prop:w,defaultProp:p,onChange:e=>{var t;null==(t=[...k.current][D.current])||t.focus(),y(e)}}),z=n.useRef(j);function C(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},n=(String(s).split(".")[1]||"").length,i=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-a)/s)*s+a,n),d=(0,o.q)(i,[a,l]);_(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,d,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*s))return e;{D.current=n.indexOf(d);let t=String(n)!==String(e);return t&&r&&x(n),t?n:e}})}return(0,h.jsx)(M,{scope:e.__scopeSlider,name:r,disabled:c,min:a,max:l,valueIndexToChangeRef:D,thumbs:k.current,values:j,orientation:u,form:S,children:(0,h.jsx)(g.Provider,{scope:e.__scopeSlider,children:(0,h.jsx)(g.Slot,{scope:e.__scopeSlider,children:(0,h.jsx)(R?E:P,{"aria-disabled":c,"data-disabled":c?"":void 0,...A,ref:t,onPointerDown:(0,i.m)(A.onPointerDown,()=>{c||(z.current=j)}),min:a,max:l,inverted:b,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(j,e);C(e,t)},onSlideMove:c?void 0:function(e){C(e,D.current)},onSlideEnd:c?void 0:function(){let e=z.current[D.current];j[D.current]!==e&&x(j)},onHomeKeyDown:()=>!c&&C(a,0,{commit:!0}),onEndKeyDown:()=>!c&&C(l,j.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!c){let e=m.includes(t.key)||t.shiftKey&&v.includes(t.key),n=D.current;C(j[n]+s*(e?10:1)*r,n,{commit:!0})}}})})})})});D.displayName=y;var[R,j]=S(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),E=n.forwardRef((e,t)=>{let{min:r,max:o,dir:i,inverted:l,onSlideStart:d,onSlideMove:u,onSlideEnd:c,onStepKeyDown:f,...p}=e,[m,v]=n.useState(null),y=(0,a.s)(t,e=>v(e)),g=n.useRef(void 0),x=(0,s.jH)(i),b="ltr"===x,S=b&&!l||!b&&l;function A(e){let t=g.current||m.getBoundingClientRect(),n=U([0,t.width],S?[r,o]:[o,r]);return g.current=t,n(e-t.left)}return(0,h.jsx)(R,{scope:e.__scopeSlider,startEdge:S?"left":"right",endEdge:S?"right":"left",direction:S?1:-1,size:"width",children:(0,h.jsx)(_,{dir:x,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=A(e.clientX);null==d||d(t)},onSlideMove:e=>{let t=A(e.clientX);null==u||u(t)},onSlideEnd:()=>{g.current=void 0,null==c||c()},onStepKeyDown:e=>{let t=w[S?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:i,onSlideStart:l,onSlideMove:d,onSlideEnd:s,onStepKeyDown:u,...c}=e,f=n.useRef(null),p=(0,a.s)(t,f),m=n.useRef(void 0),v=!i;function y(e){let t=m.current||f.current.getBoundingClientRect(),n=U([0,t.height],v?[o,r]:[r,o]);return m.current=t,n(e-t.top)}return(0,h.jsx)(R,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,h.jsx)(_,{"data-orientation":"vertical",...c,ref:p,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);null==l||l(t)},onSlideMove:e=>{let t=y(e.clientY);null==d||d(t)},onSlideEnd:()=>{m.current=void 0,null==s||s()},onStepKeyDown:e=>{let t=w[v?"from-bottom":"from-top"].includes(e.key);null==u||u({event:e,direction:t?-1:1})}})})}),_=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:a,onHomeKeyDown:l,onEndKeyDown:d,onStepKeyDown:s,...u}=e,c=k(y,r);return(0,h.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(d(e),e.preventDefault()):m.concat(v).includes(e.key)&&(s(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),a(e))})})}),z="SliderTrack",C=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=k(z,r);return(0,h.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});C.displayName=z;var H="SliderRange",I=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,i=k(H,r),l=j(H,r),d=n.useRef(null),s=(0,a.s)(t,d),u=i.values.length,c=i.values.map(e=>G(e,i.min,i.max)),p=u>1?Math.min(...c):0,m=100-Math.max(...c);return(0,h.jsx)(f.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...o,ref:s,style:{...e.style,[l.startEdge]:p+"%",[l.endEdge]:m+"%"}})});I.displayName=H;var K="SliderThumb",N=n.forwardRef((e,t)=>{let r=x(e.__scopeSlider),[o,i]=n.useState(null),l=(0,a.s)(t,e=>i(e)),d=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,h.jsx)(L,{...e,ref:l,index:d})}),L=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:l,...d}=e,s=k(K,r),u=j(K,r),[p,m]=n.useState(null),v=(0,a.s)(t,e=>m(e)),w=!p||s.form||!!p.closest("form"),y=(0,c.X)(p),x=s.values[o],b=void 0===x?0:G(x,s.min,s.max),S=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(o,s.values.length),A=null==y?void 0:y[u.size],M=A?function(e,t,r){let n=e/2,o=U([0,50],[0,n]);return(n-o(t)*r)*r}(A,b,u.direction):0;return n.useEffect(()=>{if(p)return s.thumbs.add(p),()=>{s.thumbs.delete(p)}},[p,s.thumbs]),(0,h.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:"calc(".concat(b,"% + ").concat(M,"px)")},children:[(0,h.jsx)(g.ItemSlot,{scope:e.__scopeSlider,children:(0,h.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||S,"aria-valuemin":s.min,"aria-valuenow":x,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...d,ref:v,style:void 0===x?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{s.valueIndexToChangeRef.current=o})})}),w&&(0,h.jsx)(q,{name:null!=l?l:s.name?s.name+(s.values.length>1?"[]":""):void 0,form:s.form,value:x},o)]})});N.displayName=K;var q=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:o,...i}=e,l=n.useRef(null),d=(0,a.s)(l,t),s=(0,u.Z)(o);return n.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==o&&t){let r=new Event("input",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[s,o]),(0,h.jsx)(f.sG.input,{style:{display:"none"},...i,ref:d,defaultValue:o})});function G(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function U(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}q.displayName="RadioBubbleInput";var O=D,T=C,V=I,X=N},5383:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(865).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},5943:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(865).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},8140:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(865).A)("utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])}}]);
// Unsplash image URLs for KostHub
// All images are optimized for web with proper dimensions and cropping

export const UNSPLASH_IMAGES = {
  // Kost room images - modern, clean boarding house rooms
  kost: {
    room1: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center", // Modern bedroom
    room2: "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center", // Cozy bedroom
    room3: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop&crop=center", // Minimalist room
    room4: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center", // Luxury bedroom
    room5: "https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=800&h=600&fit=crop&crop=center", // Simple room - FIXED
    room6: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center", // Contemporary room - FIXED

    // Additional room views
    interior1: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop&crop=center", // Living area
    interior2: "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center", // Kitchen area - FIXED
    interior3: "https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop&crop=center", // Study area - FIXED
  },
  
  // User avatars - diverse, professional headshots
  avatars: {
    male1: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center", // Professional male
    female1: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=100&h=100&fit=crop&crop=center", // Professional female - FIXED
    male2: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center", // Young male
    female2: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=center", // Young female - FIXED
    male3: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=center", // Casual male
    female3: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=center", // Casual female - MOVED
    male4: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=center", // Business male
    female4: "https://images.unsplash.com/photo-1607746882042-944635dfe10e?w=100&h=100&fit=crop&crop=center", // Business female - FIXED
    male5: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=100&h=100&fit=crop&crop=center", // Friendly male
    female5: "https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=100&h=100&fit=crop&crop=center", // Friendly female - FIXED
  },
  
  // Open Graph images for social sharing
  og: {
    main: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center", // Main OG image
    listings: "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center", // Listings page
    about: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1200&h=630&fit=crop&crop=center", // About page
    contact: "https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1200&h=630&fit=crop&crop=center", // Contact page - FIXED
  },
  
  // Building exteriors for kost locations
  buildings: {
    jakarta: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center", // Jakarta building
    bandung: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center", // Bandung building
    yogya: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center", // Yogya building
    surabaya: "https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&h=600&fit=crop&crop=center", // Surabaya building
  },
  
  // Facility images
  facilities: {
    wifi: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center", // WiFi setup
    parking: "https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=400&h=300&fit=crop&crop=center", // Parking area
    kitchen: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center", // Kitchen
    security: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center", // Security system
    laundry: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center", // Laundry area
  }
} as const

// Helper functions for getting images
export const getKostImage = (index: number = 0): string => {
  const images = Object.values(UNSPLASH_IMAGES.kost)
  return images[index % images.length]
}

export const getAvatarImage = (index: number = 0): string => {
  const avatars = Object.values(UNSPLASH_IMAGES.avatars)
  return avatars[index % avatars.length]
}

export const getRandomKostImages = (count: number = 3): string[] => {
  const images = Object.values(UNSPLASH_IMAGES.kost)
  const shuffled = [...images].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// Default fallback images
export const DEFAULT_KOST_IMAGE = UNSPLASH_IMAGES.kost.room1
export const DEFAULT_AVATAR_IMAGE = UNSPLASH_IMAGES.avatars.male1

// Fallback images for error cases
export const FALLBACK_IMAGES = {
  kost: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center",
  avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center",
  og: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center"
} as const

// Image optimization parameters
export const IMAGE_PARAMS = {
  quality: 80,
  format: 'webp',
  sizes: {
    thumbnail: 'w=300&h=200',
    card: 'w=400&h=300', 
    preview: 'w=800&h=600',
    hero: 'w=1200&h=800',
    og: 'w=1200&h=630',
    avatar: 'w=100&h=100'
  }
} as const

// Function to build optimized image URL
export const buildImageUrl = (baseUrl: string, size: keyof typeof IMAGE_PARAMS.sizes): string => {
  const params = IMAGE_PARAMS.sizes[size]
  const separator = baseUrl.includes('?') ? '&' : '?'
  return `${baseUrl}${separator}${params}&fit=crop&crop=center&q=${IMAGE_PARAMS.quality}`
}

// Function to get safe image URL with fallback
export const getSafeImageUrl = (imageUrl: string, type: 'kost' | 'avatar' | 'og' = 'kost'): string => {
  if (!imageUrl) {
    return FALLBACK_IMAGES[type]
  }

  // Validate Unsplash URL format
  if (!imageUrl.includes('images.unsplash.com')) {
    return FALLBACK_IMAGES[type]
  }

  return imageUrl
}

// Preload critical images
export const PRELOAD_IMAGES = [
  UNSPLASH_IMAGES.kost.room1,
  UNSPLASH_IMAGES.kost.room2,
  UNSPLASH_IMAGES.kost.room3,
] as const

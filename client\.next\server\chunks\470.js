exports.id=470,exports.ids=[470],exports.modules={6500:(a,b,c)=>{"use strict";c.d(b,{Footer:()=>d});let d=(0,c(58999).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\footer.tsx","Footer")},9954:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n,metadata:()=>m});var d=c(41063),e=c(10038),f=c.n(e),g=c(75320),h=c.n(g),i=c(40607),j=c(6500),k=c(20572),l=c(62426);c(91994);let m={title:"KostHub - Platform Pencarian Kost Inovatif",description:"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif untuk membantu Anda menemukan tempat tinggal yang sempurna.",keywords:["kost","sewa kamar","tempat tinggal","boarding house","pencarian kost","kost murah"],authors:[{name:"KostHub Team"}],creator:"KostHub",publisher:"KostHub",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://kosthub.com"),alternates:{canonical:"/"},openGraph:{title:"KostHub - Platform Pencarian Kost Inovatif",description:"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif.",url:"https://kosthub.com",siteName:"KostHub",images:[{url:l.Y$.og.main,width:1200,height:630,alt:"KostHub - Platform Pencarian Kost"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"KostHub - Platform Pencarian Kost Inovatif",description:"Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif.",images:[l.Y$.og.main]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function n({children:a}){return(0,d.jsx)("html",{lang:"id",className:"scroll-smooth",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased min-h-screen bg-background font-sans`,children:(0,d.jsx)("div",{className:"relative flex min-h-screen flex-col",children:(0,d.jsxs)(k.ErrorBoundary,{children:[(0,d.jsx)(i.Navigation,{}),(0,d.jsx)("main",{className:"flex-1",children:a}),(0,d.jsx)(j.Footer,{})]})})})})}},18729:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(20356);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},19970:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,27419,23)),Promise.resolve().then(c.t.bind(c,80442,23)),Promise.resolve().then(c.t.bind(c,96284,23)),Promise.resolve().then(c.t.bind(c,22819,23)),Promise.resolve().then(c.t.bind(c,12339,23)),Promise.resolve().then(c.t.bind(c,77563,23)),Promise.resolve().then(c.t.bind(c,99403,23)),Promise.resolve().then(c.t.bind(c,45101,23)),Promise.resolve().then(c.t.bind(c,9283,23))},20572:(a,b,c)=>{"use strict";c.d(b,{ErrorBoundary:()=>e});var d=c(58999);(0,d.registerClientReference)(function(){throw Error("Attempted to call useErrorBoundary() from the server but useErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\error-boundary.tsx","useErrorBoundary"),(0,d.registerClientReference)(function(){throw Error("Attempted to call KostCardErrorFallback() from the server but KostCardErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\error-boundary.tsx","KostCardErrorFallback"),(0,d.registerClientReference)(function(){throw Error("Attempted to call SearchErrorFallback() from the server but SearchErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\error-boundary.tsx","SearchErrorFallback"),(0,d.registerClientReference)(function(){throw Error("Attempted to call DialogErrorFallback() from the server but DialogErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\error-boundary.tsx","DialogErrorFallback");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\error-boundary.tsx","ErrorBoundary")},40607:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>d});let d=(0,c(58999).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Vicky\\project baru\\kost\\client\\components\\navigation.tsx","Navigation")},52945:(a,b,c)=>{"use strict";c.d(b,{w:()=>g});var d=c(21157);c(43616);var e=c(99170),f=c(89369);function g({className:a,orientation:b="horizontal",decorative:c=!0,...g}){return(0,d.jsx)(e.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,f.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...g})}},54474:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,49765,23)),Promise.resolve().then(c.t.bind(c,74716,23)),Promise.resolve().then(c.t.bind(c,69426,23)),Promise.resolve().then(c.t.bind(c,36133,23)),Promise.resolve().then(c.t.bind(c,95425,23)),Promise.resolve().then(c.t.bind(c,60345,23)),Promise.resolve().then(c.t.bind(c,10801,23)),Promise.resolve().then(c.t.bind(c,35555,23)),Promise.resolve().then(c.bind(c,33761))},57265:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>s});var d=c(21157),e=c(43616),f=c(99736),g=c.n(f),h=c(90574),i=c(72057),j=c(14063),k=c(83343),l=c(49679),m=c(32175),n=c(71223),o=c(85404),p=c(61804),q=c(26968);let r=[{name:"Beranda",href:"/",icon:j.A},{name:"Cari Kost",href:"/listings",icon:k.A},{name:"Favorit",href:"/favorites",icon:l.A},{name:"Tentang",href:"/about",icon:m.A},{name:"Kontak",href:"/contact",icon:n.A}];function s(){let[a,b]=(0,e.useState)(!1);return(0,d.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(o.A,{className:"h-8 w-8 text-primary"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-primary",children:"KostHub"})]}),(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:r.map(a=>(0,d.jsxs)(g(),{href:a.href,className:"flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-primary transition-colors",children:[(0,d.jsx)(a.icon,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.name})]},a.name))}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,d.jsx)(h.$,{variant:"ghost",size:"sm",children:"Masuk"}),(0,d.jsx)(h.$,{size:"sm",children:"Daftar"})]}),(0,d.jsxs)(i.cj,{open:a,onOpenChange:b,children:[(0,d.jsx)(i.CG,{asChild:!0,children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",className:"md:hidden",children:(0,d.jsx)(p.A,{className:"h-5 w-5"})})}),(0,d.jsx)(i.h,{side:"right",className:"w-80",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-4 mt-8",children:[r.map(a=>(0,d.jsxs)(g(),{href:a.href,onClick:()=>b(!1),className:"flex items-center space-x-3 text-lg font-medium text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-muted",children:[(0,d.jsx)(a.icon,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:a.name})]},a.name)),(0,d.jsxs)("div",{className:"pt-4 border-t space-y-2",children:[(0,d.jsxs)(h.$,{variant:"ghost",className:"w-full justify-start",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Masuk"]}),(0,d.jsx)(h.$,{className:"w-full justify-start",children:"Daftar"})]})]})})]})]})})})}},61370:(a,b,c)=>{"use strict";c.d(b,{ErrorBoundary:()=>n});var d=c(21157),e=c(43616),f=c.n(e),g=c(90574),h=c(80832),i=c(44172),j=c(23167),k=c(14063);class l extends f().Component{constructor(a){super(a),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(a){return{hasError:!0,error:a}}componentDidCatch(a,b){console.error("Error caught by boundary:",a,b)}render(){if(this.state.hasError){if(this.props.fallback){let a=this.props.fallback;return(0,d.jsx)(a,{error:this.state.error,resetError:this.resetError})}return(0,d.jsx)(m,{error:this.state.error,resetError:this.resetError})}return this.props.children}}function m({error:a,resetError:b}){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 bg-background",children:(0,d.jsxs)(h.Zp,{className:"w-full max-w-md",children:[(0,d.jsxs)(h.aR,{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-destructive"})}),(0,d.jsx)(h.ZB,{className:"text-xl",children:"Oops! Terjadi Kesalahan"})]}),(0,d.jsxs)(h.Wu,{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-muted-foreground text-center",children:"Maaf, terjadi kesalahan yang tidak terduga. Silakan coba lagi atau kembali ke halaman utama."}),!1,(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,d.jsxs)(g.$,{onClick:b,className:"flex-1",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Coba Lagi"]}),(0,d.jsxs)(g.$,{variant:"outline",onClick:()=>window.location.href="/",className:"flex-1",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Ke Beranda"]})]})]})]})})}let n=l},62426:(a,b,c)=>{"use strict";c.d(b,{Y$:()=>d});let d={kost:{room1:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center",room2:"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center",room3:"https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop&crop=center",room4:"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center",room5:"https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=800&h=600&fit=crop&crop=center",room6:"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center",interior1:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop&crop=center",interior2:"https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center",interior3:"https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop&crop=center"},avatars:{male1:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center",female1:"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=100&h=100&fit=crop&crop=center",male2:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center",female2:"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=center",male3:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=center",female3:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=center",male4:"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=center",female4:"https://images.unsplash.com/photo-1607746882042-944635dfe10e?w=100&h=100&fit=crop&crop=center",male5:"https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=100&h=100&fit=crop&crop=center",female5:"https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=100&h=100&fit=crop&crop=center"},og:{main:"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center",listings:"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center",about:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1200&h=630&fit=crop&crop=center",contact:"https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1200&h=630&fit=crop&crop=center"},buildings:{jakarta:"https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&crop=center",bandung:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center",yogya:"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center",surabaya:"https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&h=600&fit=crop&crop=center"},facilities:{wifi:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",parking:"https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=400&h=300&fit=crop&crop=center",kitchen:"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center",security:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",laundry:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center"}};d.kost.room1,d.avatars.male1,d.kost.room1,d.kost.room2,d.kost.room3},72057:(a,b,c)=>{"use strict";c.d(b,{CG:()=>i,Fm:()=>m,cj:()=>h,h:()=>l,qp:()=>n});var d=c(21157);c(43616);var e=c(5930),f=c(44939),g=c(89369);function h({...a}){return(0,d.jsx)(e.bL,{"data-slot":"sheet",...a})}function i({...a}){return(0,d.jsx)(e.l9,{"data-slot":"sheet-trigger",...a})}function j({...a}){return(0,d.jsx)(e.ZL,{"data-slot":"sheet-portal",...a})}function k({className:a,...b}){return(0,d.jsx)(e.hJ,{"data-slot":"sheet-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function l({className:a,children:b,side:c="right",...h}){return(0,d.jsxs)(j,{children:[(0,d.jsx)(k,{}),(0,d.jsxs)(e.UC,{"data-slot":"sheet-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===c&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===c&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===c&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===c&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...h,children:[b,(0,d.jsxs)(e.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,d.jsx)(f.A,{className:"size-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sheet-header",className:(0,g.cn)("flex flex-col gap-1.5 p-4",a),...b})}function n({className:a,...b}){return(0,d.jsx)(e.hE,{"data-slot":"sheet-title",className:(0,g.cn)("text-foreground font-semibold",a),...b})}},73322:(a,b,c)=>{"use strict";c.d(b,{Footer:()=>s});var d=c(21157),e=c(99736),f=c.n(e),g=c(90574),h=c(52945),i=c(48703),j=c(1068),k=c(22759),l=c(78774),m=c(85404),n=c(89596),o=c(71223),p=c(16635);let q={company:[{name:"Tentang Kami",href:"/about"},{name:"Karir",href:"/careers"},{name:"Blog",href:"/blog"},{name:"Press",href:"/press"}],support:[{name:"Pusat Bantuan",href:"/help"},{name:"Kontak",href:"/contact"},{name:"FAQ",href:"/faq"},{name:"Panduan",href:"/guide"}],legal:[{name:"Syarat & Ketentuan",href:"/terms"},{name:"Kebijakan Privasi",href:"/privacy"},{name:"Kebijakan Cookie",href:"/cookies"},{name:"Disclaimer",href:"/disclaimer"}],services:[{name:"Cari Kost",href:"/listings"},{name:"Daftarkan Kost",href:"/register-kost"},{name:"Verifikasi Kost",href:"/verification"},{name:"Premium",href:"/premium"}]},r=[{name:"Facebook",icon:i.A,href:"#"},{name:"Twitter",icon:j.A,href:"#"},{name:"Instagram",icon:k.A,href:"#"},{name:"YouTube",icon:l.A,href:"#"}];function s(){return(0,d.jsx)("footer",{className:"bg-muted/30 border-t",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,d.jsxs)(f(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(m.A,{className:"h-8 w-8 text-primary"}),(0,d.jsx)("span",{className:"text-2xl font-bold text-primary",children:"KostHub"})]}),(0,d.jsx)("p",{className:"text-muted-foreground leading-relaxed max-w-md",children:"Platform pencarian kost terdepan dengan fitur preview dinamis dan perbandingan interaktif. Temukan tempat tinggal ideal Anda dengan mudah dan aman."}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"<EMAIL>"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"+62 21 1234 5678"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Jakarta, Indonesia"})]})]}),(0,d.jsx)("div",{className:"flex space-x-2",children:r.map(a=>(0,d.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-9 w-9 p-0",asChild:!0,children:(0,d.jsx)(f(),{href:a.href,"aria-label":a.name,children:(0,d.jsx)(a.icon,{className:"h-4 w-4"})})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Perusahaan"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.company.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Layanan"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.services.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Dukungan"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.support.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-primary transition-colors",children:a.name})},a.name))}),(0,d.jsxs)("div",{className:"pt-2",children:[(0,d.jsx)("h4",{className:"font-medium text-sm mb-2",children:"Legal"}),(0,d.jsx)("ul",{className:"space-y-1",children:q.legal.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-xs text-muted-foreground hover:text-primary transition-colors",children:a.name})},a.name))})]})]})]}),(0,d.jsx)(h.w,{className:"my-8"}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 KostHub. Semua hak dilindungi."}),(0,d.jsx)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:(0,d.jsx)("span",{children:"Dibuat dengan ❤️ di Indonesia"})})]})]})})}},73443:(a,b,c)=>{Promise.resolve().then(c.bind(c,20572)),Promise.resolve().then(c.bind(c,6500)),Promise.resolve().then(c.bind(c,40607))},80832:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g,wL:()=>j});var d=c(21157);c(43616);var e=c(89369);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-footer",className:(0,e.cn)("flex items-center px-6 [.border-t]:pt-6",a),...b})}},87515:(a,b,c)=>{Promise.resolve().then(c.bind(c,61370)),Promise.resolve().then(c.bind(c,73322)),Promise.resolve().then(c.bind(c,57265))},89369:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(21982),e=c(83162);function f(...a){return(0,e.QP)((0,d.$)(a))}},90574:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(21157);c(43616);var e=c(84726),f=c(59542),g=c(89369);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},91994:()=>{}};
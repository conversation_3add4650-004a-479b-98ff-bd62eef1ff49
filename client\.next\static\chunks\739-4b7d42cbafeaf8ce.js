"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[739],{2329:(e,t,n)=>{n.d(t,{jH:()=>l});var r=n(5461);n(2273);var i=r.createContext(void 0);function l(e){let t=r.useContext(i);return e||t||"ltr"}},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(865).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},4137:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(865).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},4344:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(865).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5749:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(5461);function i(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6310:(e,t,n)=>{n.d(t,{UC:()=>ne,In:()=>t9,q7:()=>nn,VF:()=>ni,p4:()=>nr,ZL:()=>t8,bL:()=>t6,wn:()=>no,PP:()=>nl,l9:()=>t7,WT:()=>t4,LM:()=>nt});var r=n(5461),i=n(3816),l=n(8777),o=n(582),a=n(7698),s=n(7239),u=n(4284),c=n(2329),f=n(99),d=n(3278),p=n(9092),h=n(5821);let m=["top","right","bottom","left"],v=Math.min,g=Math.max,y=Math.round,w=Math.floor,x=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function R(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function A(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function k(e){return"y"===e?"height":"width"}let E=new Set(["top","bottom"]);function j(e){return E.has(C(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>S[e])}let L=["left","right"],N=["right","left"],M=["top","bottom"],O=["bottom","top"];function D(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function I(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function H(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function z(e,t,n){let r,{reference:i,floating:l}=e,o=j(t),a=T(j(t)),s=k(a),u=C(t),c="y"===o,f=i.x+i.width/2-l.width/2,d=i.y+i.height/2-l.height/2,p=i[s]/2-l[s]/2;switch(u){case"top":r={x:f,y:i.y-l.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-l.width,y:d};break;default:r={x:i.x,y:i.y}}switch(A(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let B=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:l=[],platform:o}=n,a=l.filter(Boolean),s=await (null==o.isRTL?void 0:o.isRTL(t)),u=await o.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=z(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:o,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=g?g:f,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await o.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:f}=z(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:p}};async function F(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:l,rects:o,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=R(t,e),h=I(p),m=a[d?"floating"===f?"reference":"floating":f],v=H(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(m)))||n?m:m.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===f?{x:r,y:i,width:o.floating.width,height:o.floating.height}:o.reference,y=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(y))&&await (null==l.getScale?void 0:l.getScale(y))||{x:1,y:1},x=H(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function W(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function V(e){return m.some(t=>e[t]>=0)}let _=new Set(["left","top"]);async function G(e,t){let{placement:n,platform:r,elements:i}=e,l=await (null==r.isRTL?void 0:r.isRTL(i.floating)),o=C(n),a=A(n),s="y"===j(n),u=_.has(o)?-1:1,c=l&&s?-1:1,f=R(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:d*u}:{x:d*u,y:p*c}}function q(){return"undefined"!=typeof window}function K(e){return Y(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function X(e){var t;return null==(t=(Y(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Y(e){return!!q()&&(e instanceof Node||e instanceof U(e).Node)}function Z(e){return!!q()&&(e instanceof Element||e instanceof U(e).Element)}function $(e){return!!q()&&(e instanceof HTMLElement||e instanceof U(e).HTMLElement)}function J(e){return!!q()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof U(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ef(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(i)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ei=["transform","translate","scale","rotate","perspective"],el=["transform","translate","scale","rotate","perspective","filter"],eo=["paint","layout","strict","content"];function ea(e){let t=es(),n=Z(e)?ef(e):e;return ei.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||el.some(e=>(n.willChange||"").includes(e))||eo.some(e=>(n.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eu=new Set(["html","body","#document"]);function ec(e){return eu.has(K(e))}function ef(e){return U(e).getComputedStyle(e)}function ed(e){return Z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===K(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||X(e);return J(t)?t.host:t}function eh(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=ep(t);return ec(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&ee(n)?n:e(n)}(e),l=i===(null==(r=e.ownerDocument)?void 0:r.body),o=U(i);if(l){let e=em(o);return t.concat(o,o.visualViewport||[],ee(i)?i:[],e&&n?eh(e):[])}return t.concat(i,eh(i,[],n))}function em(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ev(e){let t=ef(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=$(e),l=i?e.offsetWidth:n,o=i?e.offsetHeight:r,a=y(n)!==l||y(r)!==o;return a&&(n=l,r=o),{width:n,height:r,$:a}}function eg(e){return Z(e)?e:e.contextElement}function ey(e){let t=eg(e);if(!$(t))return x(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:l}=ev(t),o=(l?y(n.width):n.width)/r,a=(l?y(n.height):n.height)/i;return o&&Number.isFinite(o)||(o=1),a&&Number.isFinite(a)||(a=1),{x:o,y:a}}let ew=x(0);function ex(e){let t=U(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ew}function eb(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),o=eg(e),a=x(1);t&&(r?Z(r)&&(a=ey(r)):a=ey(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===U(o))&&i)?ex(o):x(0),u=(l.left+s.x)/a.x,c=(l.top+s.y)/a.y,f=l.width/a.x,d=l.height/a.y;if(o){let e=U(o),t=r&&Z(r)?U(r):r,n=e,i=em(n);for(;i&&r&&t!==n;){let e=ey(i),t=i.getBoundingClientRect(),r=ef(i),l=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,o=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=l,c+=o,i=em(n=U(i))}}return H({width:f,height:d,x:u,y:c})}function eS(e,t){let n=ed(e).scrollLeft;return t?t.left+n:eb(X(e)).left+n}function eR(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eS(e,r)),y:r.top+t.scrollTop}}let eC=new Set(["absolute","fixed"]);function eA(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=U(e),r=X(e),i=n.visualViewport,l=r.clientWidth,o=r.clientHeight,a=0,s=0;if(i){l=i.width,o=i.height;let e=es();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:l,height:o,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=X(e),n=ed(e),r=e.ownerDocument.body,i=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),o=-n.scrollLeft+eS(e),a=-n.scrollTop;return"rtl"===ef(r).direction&&(o+=g(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:o,y:a}}(X(e));else if(Z(t))r=function(e,t){let n=eb(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,l=$(e)?ey(e):x(1),o=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:o,height:a,x:i*l.x,y:r*l.y}}(t,n);else{let n=ex(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return H(r)}function eT(e){return"static"===ef(e).position}function ek(e,t){if(!$(e)||"fixed"===ef(e).position)return null;if(t)return t(e);let n=e.offsetParent;return X(e)===n&&(n=n.ownerDocument.body),n}function eE(e,t){var n;let r=U(e);if(er(e))return r;if(!$(e)){let t=ep(e);for(;t&&!ec(t);){if(Z(t)&&!eT(t))return t;t=ep(t)}return r}let i=ek(e,t);for(;i&&(n=i,et.has(K(n)))&&eT(i);)i=ek(i,t);return i&&ec(i)&&eT(i)&&!ea(i)?r:i||function(e){let t=ep(e);for(;$(t)&&!ec(t);){if(ea(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let ej=async function(e){let t=this.getOffsetParent||eE,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),i=X(t),l="fixed"===n,o=eb(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=x(0);if(r||!r&&!l)if(("body"!==K(t)||ee(i))&&(a=ed(t)),r){let e=eb(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=eS(i));l&&!r&&i&&(s.x=eS(i));let u=!i||r||l?x(0):eR(i,a);return{x:o.left+a.scrollLeft-s.x-u.x,y:o.top+a.scrollTop-s.y-u.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eP={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,l="fixed"===i,o=X(r),a=!!t&&er(t.floating);if(r===o||a&&l)return n;let s={scrollLeft:0,scrollTop:0},u=x(1),c=x(0),f=$(r);if((f||!f&&!l)&&(("body"!==K(r)||ee(o))&&(s=ed(r)),$(r))){let e=eb(r);u=ey(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!o||f||l?x(0):eR(o,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:X,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,l=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eh(e,[],!1).filter(e=>Z(e)&&"body"!==K(e)),i=null,l="fixed"===ef(e).position,o=l?ep(e):e;for(;Z(o)&&!ec(o);){let t=ef(o),n=ea(o);n||"fixed"!==t.position||(i=null),(l?!n&&!i:!n&&"static"===t.position&&!!i&&eC.has(i.position)||ee(o)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!Z(r)||ec(r))&&("fixed"===ef(r).position||e(r,n))}(e,o))?r=r.filter(e=>e!==o):i=t,o=ep(o)}return t.set(e,r),r}(t,this._c):[].concat(n),r],o=l[0],a=l.reduce((e,n)=>{let r=eA(t,n,i);return e.top=g(r.top,e.top),e.right=v(r.right,e.right),e.bottom=v(r.bottom,e.bottom),e.left=g(r.left,e.left),e},eA(t,o,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eE,getElementRects:ej,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ev(e);return{width:t,height:n}},getScale:ey,isElement:Z,isRTL:function(e){return"rtl"===ef(e).direction}};function eL(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eN=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:l,platform:o,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=R(e,t)||{};if(null==u)return{};let f=I(c),d={x:n,y:r},p=T(j(i)),h=k(p),m=await o.getDimensions(u),y="y"===p,w=y?"clientHeight":"clientWidth",x=l.reference[h]+l.reference[p]-d[p]-l.floating[h],b=d[p]-l.reference[p],S=await (null==o.getOffsetParent?void 0:o.getOffsetParent(u)),C=S?S[w]:0;C&&await (null==o.isElement?void 0:o.isElement(S))||(C=a.floating[w]||l.floating[h]);let E=C/2-m[h]/2-1,P=v(f[y?"top":"left"],E),L=v(f[y?"bottom":"right"],E),N=C-m[h]-L,M=C/2-m[h]/2+(x/2-b/2),O=g(P,v(M,N)),D=!s.arrow&&null!=A(i)&&M!==O&&l.reference[h]/2-(M<P?P:L)-m[h]/2<0,H=D?M<P?M-P:M-N:0;return{[p]:d[p]+H,data:{[p]:O,centerOffset:M-O-H,...D&&{alignmentOffset:H}},reset:D}}});var eM="undefined"!=typeof document?r.useLayoutEffect:function(){};function eO(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eO(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eO(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eD(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eI(e,t){let n=eD(e);return Math.round(t*n)/n}function eH(e){let t=r.useRef(e);return eM(()=>{t.current=e}),t}var ez=n(3713),eB=n(2273),eF=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...l}=e;return(0,eB.jsx)(ez.sG.svg,{...l,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eB.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eF.displayName="Arrow";var eW=n(8658),eV=n(1465),e_=n(9241),eG="Popper",[eq,eK]=(0,u.A)(eG),[eU,eX]=eq(eG),eY=e=>{let{__scopePopper:t,children:n}=e,[i,l]=r.useState(null);return(0,eB.jsx)(eU,{scope:t,anchor:i,onAnchorChange:l,children:n})};eY.displayName=eG;var eZ="PopperAnchor",e$=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...l}=e,o=eX(eZ,n),a=r.useRef(null),u=(0,s.s)(t,a);return r.useEffect(()=>{o.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eB.jsx)(ez.sG.div,{...l,ref:u})});e$.displayName=eZ;var eJ="PopperContent",[eQ,e0]=eq(eJ),e1=r.forwardRef((e,t)=>{var n,l,o,a,u,c,f,d;let{__scopePopper:p,side:h="bottom",sideOffset:m=0,align:y="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:S=!0,collisionBoundary:E=[],collisionPadding:I=0,sticky:H="partial",hideWhenDetached:z=!1,updatePositionStrategy:q="optimized",onPlaced:K,...U}=e,Y=eX(eJ,p),[Z,$]=r.useState(null),J=(0,s.s)(t,e=>$(e)),[Q,ee]=r.useState(null),et=(0,e_.X)(Q),en=null!=(f=null==et?void 0:et.width)?f:0,er=null!=(d=null==et?void 0:et.height)?d:0,ei="number"==typeof I?I:{top:0,right:0,bottom:0,left:0,...I},el=Array.isArray(E)?E:[E],eo=el.length>0,ea={padding:ei,boundary:el.filter(e6),altBoundary:eo},{refs:es,floatingStyles:eu,placement:ec,isPositioned:ef,middlewareData:ed}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:l=[],platform:o,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:f}=e,[d,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(l);eO(h,l)||m(l);let[v,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,w(e))},[]),S=a||v,R=s||y,C=r.useRef(null),A=r.useRef(null),T=r.useRef(d),k=null!=c,E=eH(c),j=eH(o),P=eH(f),L=r.useCallback(()=>{if(!C.current||!A.current)return;let e={placement:t,strategy:n,middleware:h};j.current&&(e.platform=j.current),((e,t,n)=>{let r=new Map,i={platform:eP,...n},l={...i.platform,_c:r};return B(e,t,{...i,platform:l})})(C.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};N.current&&!eO(T.current,t)&&(T.current=t,i.flushSync(()=>{p(t)}))})},[h,t,n,j,P]);eM(()=>{!1===f&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[f]);let N=r.useRef(!1);eM(()=>(N.current=!0,()=>{N.current=!1}),[]),eM(()=>{if(S&&(C.current=S),R&&(A.current=R),S&&R){if(E.current)return E.current(S,R,L);L()}},[S,R,L,E,k]);let M=r.useMemo(()=>({reference:C,floating:A,setReference:x,setFloating:b}),[x,b]),O=r.useMemo(()=>({reference:S,floating:R}),[S,R]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eI(O.floating,d.x),r=eI(O.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eD(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,O.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:L,refs:M,elements:O,floatingStyles:D}),[d,L,M,O,D])}({strategy:"fixed",placement:h+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:o=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=eg(e),f=l||o?[...c?eh(c):[],...eh(t)]:[];f.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),o&&e.addEventListener("resize",n)});let d=c&&s?function(e,t){let n,r=null,i=X(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function o(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=u;if(a||t(),!d||!p)return;let h=w(f),m=w(i.clientWidth-(c+d)),y={rootMargin:-h+"px "+-m+"px "+-w(i.clientHeight-(f+p))+"px "+-w(c)+"px",threshold:g(0,v(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return o();r?o(!1,r):n=setTimeout(()=>{o(!1,1e-7)},1e3)}1!==r||eL(u,e.getBoundingClientRect())||o(),x=!1}try{r=new IntersectionObserver(b,{...y,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),l}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?eb(e):null;return u&&function t(){let r=eb(e);m&&!eL(m,r)&&n(),m=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{l&&e.removeEventListener("scroll",n),o&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===q})},elements:{reference:Y.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:l,placement:o,middlewareData:a}=t,s=await G(t,e);return o===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+s.x,y:l+s.y,data:{...s,placement:o}}}}}(e),options:[e,t]}))({mainAxis:m+er,alignmentAxis:x}),S&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:l=!0,crossAxis:o=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=R(e,t),u={x:n,y:r},c=await F(t,s),f=j(C(i)),d=T(f),p=u[d],h=u[f];if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=g(n,v(p,r))}if(o){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=g(n,v(h,r))}let m=a.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:l,[f]:o}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===H?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:l,middlewareData:o}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=R(e,t),c={x:n,y:r},f=j(i),d=T(f),p=c[d],h=c[f],m=R(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=l.reference[d]-l.floating[e]+v.mainAxis,n=l.reference[d]+l.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;let e="y"===d?"width":"height",t=_.has(C(i)),n=l.reference[f]-l.floating[e]+(t&&(null==(g=o.offset)?void 0:g[f])||0)+(t?0:v.crossAxis),r=l.reference[f]+l.reference[e]+(t?0:(null==(y=o.offset)?void 0:y[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}))():void 0,...ea}),S&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,l,o;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=R(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=C(a),b=j(c),S=C(c)===c,E=await (null==f.isRTL?void 0:f.isRTL(d.floating)),I=m||(S||!y?[D(c)]:function(e){let t=D(e);return[P(e),t,P(t)]}(c)),H="none"!==g;!m&&H&&I.push(...function(e,t,n,r){let i=A(e),l=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?N:L;return t?L:N;case"left":case"right":return t?M:O;default:return[]}}(C(e),"start"===n,r);return i&&(l=l.map(e=>e+"-"+i),t&&(l=l.concat(l.map(P)))),l}(c,y,g,E));let z=[c,...I],B=await F(t,w),W=[],V=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&W.push(B[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=A(e),i=T(j(e)),l=k(i),o="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(o=D(o)),[o,D(o)]}(a,u,E);W.push(B[e[0]],B[e[1]])}if(V=[...V,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=z[e];if(t&&("alignment"!==h||b===j(t)||V.every(e=>e.overflows[0]>0&&j(e.placement)===b)))return{data:{index:e,overflows:V},reset:{placement:t}};let n=null==(l=V.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(v){case"bestFit":{let e=null==(o=V.filter(e=>{if(H){let t=j(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,l,{placement:o,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...f}=R(e,t),d=await F(t,f),p=C(o),h=A(o),m="y"===j(o),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(i=p,l=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(l=p,i="end"===h?"top":"bottom");let x=w-d.top-d.bottom,b=y-d.left-d.right,S=v(w-d[i],x),T=v(y-d[l],b),k=!t.middlewareData.shift,E=S,P=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=x),k&&!h){let e=g(d.left,0),t=g(d.right,0),n=g(d.top,0),r=g(d.bottom,0);m?P=y-2*(0!==e||0!==t?e+t:g(d.left,d.right)):E=w-2*(0!==n||0!==r?n+r:g(d.top,d.bottom))}await c({...t,availableWidth:P,availableHeight:E});let L=await s.getDimensions(u.floating);return y!==L.width||w!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:l,height:o}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(o,"px"))}}),Q&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eN({element:n.current,padding:r}).fn(t):{}:n?eN({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:Q,padding:b}),e7({arrowWidth:en,arrowHeight:er}),z&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=R(e,t);switch(r){case"referenceHidden":{let e=W(await F(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:V(e)}}}case"escaped":{let e=W(await F(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:V(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[ep,em]=e4(ec),ev=(0,eW.c)(K);(0,eV.N)(()=>{ef&&(null==ev||ev())},[ef,ev]);let ey=null==(n=ed.arrow)?void 0:n.x,ew=null==(l=ed.arrow)?void 0:l.y,ex=(null==(o=ed.arrow)?void 0:o.centerOffset)!==0,[eS,eR]=r.useState();return(0,eV.N)(()=>{Z&&eR(window.getComputedStyle(Z).zIndex)},[Z]),(0,eB.jsx)("div",{ref:es.setFloating,"data-radix-popper-content-wrapper":"",style:{...eu,transform:ef?eu.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eS,"--radix-popper-transform-origin":[null==(a=ed.transformOrigin)?void 0:a.x,null==(u=ed.transformOrigin)?void 0:u.y].join(" "),...(null==(c=ed.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eB.jsx)(eQ,{scope:p,placedSide:ep,onArrowChange:ee,arrowX:ey,arrowY:ew,shouldHideArrow:ex,children:(0,eB.jsx)(ez.sG.div,{"data-side":ep,"data-align":em,...U,ref:J,style:{...U.style,animation:ef?void 0:"none"}})})})});e1.displayName=eJ;var e2="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e3=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e0(e2,n),l=e5[i.placedSide];return(0,eB.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eB.jsx)(eF,{...r,ref:t,style:{...r.style,display:"block"}})})});function e6(e){return null!==e}e3.displayName=e2;var e7=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,l,o;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=e4(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+f/2,g=(null!=(o=null==(i=u.arrow)?void 0:i.y)?o:0)+d/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(s.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+d,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function e4(e){let[t,n="center"]=e.split("-");return[t,n]}var e9=n(4820),e8=n(3330),te=n(4976),tt=n(5749),tn=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,eB.jsx)(ez.sG.span,{...e,ref:t,style:{...tn,...e.style}})).displayName="VisuallyHidden";var tr=n(3767),ti=n(8571),tl=[" ","Enter","ArrowUp","ArrowDown"],to=[" ","Enter"],ta="Select",[ts,tu,tc]=(0,a.N)(ta),[tf,td]=(0,u.A)(ta,[tc,eK]),tp=eK(),[th,tm]=tf(ta),[tv,tg]=tf(ta),ty=e=>{let{__scopeSelect:t,children:n,open:i,defaultOpen:l,onOpenChange:o,value:a,defaultValue:s,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:m,required:v,form:g}=e,y=tp(t),[w,x]=r.useState(null),[b,S]=r.useState(null),[R,C]=r.useState(!1),A=(0,c.jH)(f),[T,k]=(0,te.i)({prop:i,defaultProp:null!=l&&l,onChange:o,caller:ta}),[E,j]=(0,te.i)({prop:a,defaultProp:s,onChange:u,caller:ta}),P=r.useRef(null),L=!w||g||!!w.closest("form"),[N,M]=r.useState(new Set),O=Array.from(N).map(e=>e.props.value).join(";");return(0,eB.jsx)(eY,{...y,children:(0,eB.jsxs)(th,{required:v,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:R,onValueNodeHasChildrenChange:C,contentId:(0,h.B)(),value:E,onValueChange:j,open:T,onOpenChange:k,dir:A,triggerPointerDownPosRef:P,disabled:m,children:[(0,eB.jsx)(ts.Provider,{scope:t,children:(0,eB.jsx)(tv,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),L?(0,eB.jsxs)(t1,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:p,value:E,onChange:e=>j(e.target.value),disabled:m,form:g,children:[void 0===E?(0,eB.jsx)("option",{value:""}):null,Array.from(N)]},O):null]})})};ty.displayName=ta;var tw="SelectTrigger",tx=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:i=!1,...l}=e,a=tp(n),u=tm(tw,n),c=u.disabled||i,f=(0,s.s)(t,u.onTriggerChange),d=tu(n),p=r.useRef("touch"),[h,m,v]=t5(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=t3(t,e,n);void 0!==r&&u.onValueChange(r.value)}),g=e=>{c||(u.onOpenChange(!0),v()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eB.jsx)(e$,{asChild:!0,...a,children:(0,eB.jsx)(ez.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":t2(u.value)?"":void 0,...l,ref:f,onClick:(0,o.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,o.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,o.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&tl.includes(e.key)&&(g(),e.preventDefault())})})})});tx.displayName=tw;var tb="SelectValue",tS=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,children:l,placeholder:o="",...a}=e,u=tm(tb,n),{onValueNodeHasChildrenChange:c}=u,f=void 0!==l,d=(0,s.s)(t,u.onValueNodeChange);return(0,eV.N)(()=>{c(f)},[c,f]),(0,eB.jsx)(ez.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:t2(u.value)?(0,eB.jsx)(eB.Fragment,{children:o}):l})});tS.displayName=tb;var tR=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...i}=e;return(0,eB.jsx)(ez.sG.span,{"aria-hidden":!0,...i,ref:t,children:r||"▼"})});tR.displayName="SelectIcon";var tC=e=>(0,eB.jsx)(e9.Z,{asChild:!0,...e});tC.displayName="SelectPortal";var tA="SelectContent",tT=r.forwardRef((e,t)=>{let n=tm(tA,e.__scopeSelect),[l,o]=r.useState();return((0,eV.N)(()=>{o(new DocumentFragment)},[]),n.open)?(0,eB.jsx)(tP,{...e,ref:t}):l?i.createPortal((0,eB.jsx)(tk,{scope:e.__scopeSelect,children:(0,eB.jsx)(ts.Slot,{scope:e.__scopeSelect,children:(0,eB.jsx)("div",{children:e.children})})}),l):null});tT.displayName=tA;var[tk,tE]=tf(tA),tj=(0,e8.TL)("SelectContent.RemoveScroll"),tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:i="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S,...R}=e,C=tm(tA,n),[A,T]=r.useState(null),[k,E]=r.useState(null),j=(0,s.s)(t,e=>T(e)),[P,L]=r.useState(null),[N,M]=r.useState(null),O=tu(n),[D,I]=r.useState(!1),H=r.useRef(!1);r.useEffect(()=>{if(A)return(0,tr.Eq)(A)},[A]),(0,d.Oh)();let z=r.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),i=document.activeElement;for(let n of e)if(n===i||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&k&&(k.scrollTop=0),n===r&&k&&(k.scrollTop=k.scrollHeight),null==n||n.focus(),document.activeElement!==i))return},[O,k]),B=r.useCallback(()=>z([P,A]),[z,P,A]);r.useEffect(()=>{D&&B()},[D,B]);let{onOpenChange:F,triggerPointerDownPosRef:W}=C;r.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,i,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(i=null==(n=W.current)?void 0:n.x)?i:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=W.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||F(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,F,W]),r.useEffect(()=>{let e=()=>F(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[F]);let[V,_]=t5(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=t3(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&(L(e),r&&(H.current=!0))},[C.value]),q=r.useCallback(()=>null==A?void 0:A.focus(),[A]),K=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&M(e)},[C.value]),U="popper"===i?tN:tL,X=U===tN?{side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,eB.jsx)(tk,{scope:n,content:A,viewport:k,onViewportChange:E,itemRefCallback:G,selectedItem:P,onItemLeave:q,itemTextRefCallback:K,focusSelectedItem:B,selectedItemText:N,position:i,isPositioned:D,searchRef:V,children:(0,eB.jsx)(ti.A,{as:tj,allowPinchZoom:!0,children:(0,eB.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.m)(l,e=>{var t;null==(t=C.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eB.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eB.jsx)(U,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...R,...X,onPlaced:()=>I(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,o.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||_(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});tP.displayName="SelectContentImpl";var tL=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:i,...o}=e,a=tm(tA,n),u=tE(tA,n),[c,f]=r.useState(null),[d,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=tu(n),v=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:b}=u,S=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&y&&w&&x){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){let i=r.left-t.left,o=n.left-i,a=e.left-o,s=e.width+a,u=Math.max(s,t.width),f=window.innerWidth-10,d=(0,l.q)(o,[10,Math.max(10,f-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let i=t.right-r.right,o=window.innerWidth-n.right-i,a=window.innerWidth-e.right-o,s=e.width+a,u=Math.max(s,t.width),f=window.innerWidth-10,d=(0,l.q)(o,[10,Math.max(10,f-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let o=m(),s=window.innerHeight-20,u=y.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),b=p+h+u+parseInt(f.paddingBottom,10)+g,S=Math.min(5*w.offsetHeight,b),R=window.getComputedStyle(y),C=parseInt(R.paddingTop,10),A=parseInt(R.paddingBottom,10),T=e.top+e.height/2-10,k=w.offsetHeight/2,E=p+h+(w.offsetTop+k);if(E<=T){let e=o.length>0&&w===o[o.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-T,k+(e?A:0)+(d.clientHeight-y.offsetTop-y.offsetHeight)+g);c.style.height=E+t+"px"}else{let e=o.length>0&&w===o[0].ref.current;c.style.top="0px";let t=Math.max(T,p+y.offsetTop+(e?C:0)+k);c.style.height=t+(b-E)+"px",y.scrollTop=E-T+y.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=S+"px",c.style.maxHeight=s+"px",null==i||i(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,c,d,y,w,x,a.dir,i]);(0,eV.N)(()=>S(),[S]);let[R,C]=r.useState();(0,eV.N)(()=>{d&&C(window.getComputedStyle(d).zIndex)},[d]);let A=r.useCallback(e=>{e&&!0===g.current&&(S(),null==b||b(),g.current=!1)},[S,b]);return(0,eB.jsx)(tM,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:A,children:(0,eB.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,eB.jsx)(ez.sG.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});tL.displayName="SelectItemAlignedPosition";var tN=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:i=10,...l}=e,o=tp(n);return(0,eB.jsx)(e1,{...o,...l,ref:t,align:r,collisionPadding:i,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tN.displayName="SelectPopperPosition";var[tM,tO]=tf(tA,{}),tD="SelectViewport",tI=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:i,...l}=e,a=tE(tD,n),u=tO(tD,n),c=(0,s.s)(t,a.onViewportChange),f=r.useRef(0);return(0,eB.jsxs)(eB.Fragment,{children:[(0,eB.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,eB.jsx)(ts.Slot,{scope:n,children:(0,eB.jsx)(ez.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,o.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,i=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(i<r){let l=i+e,o=Math.min(r,l),a=l-o;n.style.height=o+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});tI.displayName=tD;var tH="SelectGroup",[tz,tB]=tf(tH);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,i=(0,h.B)();return(0,eB.jsx)(tz,{scope:n,id:i,children:(0,eB.jsx)(ez.sG.div,{role:"group","aria-labelledby":i,...r,ref:t})})}).displayName=tH;var tF="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,i=tB(tF,n);return(0,eB.jsx)(ez.sG.div,{id:i.id,...r,ref:t})}).displayName=tF;var tW="SelectItem",[tV,t_]=tf(tW),tG=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:i,disabled:l=!1,textValue:a,...u}=e,c=tm(tW,n),f=tE(tW,n),d=c.value===i,[p,m]=r.useState(null!=a?a:""),[v,g]=r.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=f.itemRefCallback)?void 0:t.call(f,e,i,l)}),w=(0,h.B)(),x=r.useRef("touch"),b=()=>{l||(c.onValueChange(i),c.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eB.jsx)(tV,{scope:n,value:i,disabled:l,textId:w,isSelected:d,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,eB.jsx)(ts.ItemSlot,{scope:n,value:i,disabled:l,textValue:p,children:(0,eB.jsx)(ez.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":d&&v,"data-state":d?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:y,onFocus:(0,o.m)(u.onFocus,()=>g(!0)),onBlur:(0,o.m)(u.onBlur,()=>g(!1)),onClick:(0,o.m)(u.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,o.m)(u.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,o.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,o.m)(u.onPointerMove,e=>{if(x.current=e.pointerType,l){var t;null==(t=f.onItemLeave)||t.call(f)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=f.onItemLeave)||t.call(f)}}),onKeyDown:(0,o.m)(u.onKeyDown,e=>{var t;((null==(t=f.searchRef)?void 0:t.current)===""||" "!==e.key)&&(to.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});tG.displayName=tW;var tq="SelectItemText",tK=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:o,...a}=e,u=tm(tq,n),c=tE(tq,n),f=t_(tq,n),d=tg(tq,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),f.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,f.value,f.disabled)}),v=null==p?void 0:p.textContent,g=r.useMemo(()=>(0,eB.jsx)("option",{value:f.value,disabled:f.disabled,children:v},f.value),[f.disabled,f.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=d;return(0,eV.N)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eB.jsxs)(eB.Fragment,{children:[(0,eB.jsx)(ez.sG.span,{id:f.textId,...a,ref:m}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?i.createPortal(a.children,u.valueNode):null]})});tK.displayName=tq;var tU="SelectItemIndicator",tX=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t_(tU,n).isSelected?(0,eB.jsx)(ez.sG.span,{"aria-hidden":!0,...r,ref:t}):null});tX.displayName=tU;var tY="SelectScrollUpButton",tZ=r.forwardRef((e,t)=>{let n=tE(tY,e.__scopeSelect),i=tO(tY,e.__scopeSelect),[l,o]=r.useState(!1),a=(0,s.s)(t,i.onScrollButtonChange);return(0,eV.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){o(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,eB.jsx)(tQ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tZ.displayName=tY;var t$="SelectScrollDownButton",tJ=r.forwardRef((e,t)=>{let n=tE(t$,e.__scopeSelect),i=tO(t$,e.__scopeSelect),[l,o]=r.useState(!1),a=(0,s.s)(t,i.onScrollButtonChange);return(0,eV.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,eB.jsx)(tQ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tJ.displayName=t$;var tQ=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:i,...l}=e,a=tE("SelectScrollButton",n),s=r.useRef(null),u=tu(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,eV.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,eB.jsx)(ez.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,o.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(i,50))}),onPointerMove:(0,o.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(i,50))}),onPointerLeave:(0,o.m)(l.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eB.jsx)(ez.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var t0="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,i=tp(n),l=tm(t0,n),o=tE(t0,n);return l.open&&"popper"===o.position?(0,eB.jsx)(e3,{...i,...r,ref:t}):null}).displayName=t0;var t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:i,...l}=e,o=r.useRef(null),a=(0,s.s)(t,o),u=(0,tt.Z)(i);return r.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==i&&t){let n=new Event("change",{bubbles:!0});t.call(e,i),e.dispatchEvent(n)}},[u,i]),(0,eB.jsx)(ez.sG.select,{...l,style:{...tn,...l.style},ref:a,defaultValue:i})});function t2(e){return""===e||void 0===e}function t5(e){let t=(0,eW.c)(e),n=r.useRef(""),i=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),o=r.useCallback(()=>{n.current="",window.clearTimeout(i.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(i.current),[]),[n,l,o]}function t3(e,t,n){var r,i;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1,a=(r=e,i=Math.max(o,0),r.map((e,t)=>r[(i+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}t1.displayName="SelectBubbleInput";var t6=ty,t7=tx,t4=tS,t9=tR,t8=tC,ne=tT,nt=tI,nn=tG,nr=tK,ni=tX,nl=tZ,no=tJ},7698:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function i(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function l(e,t,n){var i=r(e,t,"set");if(i.set)i.set.call(e,n);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=n}return n}n.d(t,{N:()=>d});var o,a=n(5461),s=n(4284),u=n(7239),c=n(3330),f=n(2273);function d(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[i,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:n}=e,r=a.useRef(null),l=a.useRef(new Map).current;return(0,f.jsx)(i,{scope:t,itemMap:l,collectionRef:r,children:n})};o.displayName=t;let d=e+"CollectionSlot",p=(0,c.TL)(d),h=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=l(d,n),o=(0,u.s)(t,i.collectionRef);return(0,f.jsx)(p,{ref:o,children:r})});h.displayName=d;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,c.TL)(m),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,o=a.useRef(null),s=(0,u.s)(t,o),c=l(m,n);return a.useEffect(()=>(c.itemMap.set(o,{ref:o,...i}),()=>void c.itemMap.delete(o))),(0,f.jsx)(g,{...{[v]:""},ref:s,children:r})});return y.displayName=m,[{Provider:o,Slot:h,ItemSlot:y},function(t){let n=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),i=r>=0?r:n+r;return i<0||i>=n?-1:i}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap,class e extends Map{set(e,t){return p.get(this)&&(this.has(e)?i(this,o)[i(this,o).indexOf(e)]=e:i(this,o).push(e)),super.set(e,t),this}insert(e,t,n){let r,l=this.has(t),a=i(this,o).length,s=m(e),u=s>=0?s:a+s,c=u<0||u>=a?-1:u;if(c===this.size||l&&c===this.size-1||-1===c)return this.set(t,n),this;let f=this.size+ +!l;s<0&&u++;let d=[...i(this,o)],p=!1;for(let e=u;e<f;e++)if(u===e){let i=d[e];d[e]===t&&(i=d[e+1]),l&&this.delete(t),r=this.get(i),this.set(t,n)}else{p||d[e-1]!==t||(p=!0);let n=d[p?e:e-1],i=r;r=this.get(n),this.delete(n),this.set(n,i)}return this}with(t,n,r){let i=new e(this);return i.insert(t,n,r),i}before(e){let t=i(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=i(this,o).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=i(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=i(this,o).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return l(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&i(this,o).splice(i(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=h(i(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=h(i(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return i(this,o).indexOf(e)}keyAt(e){return h(i(this,o),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],i=0;for(let e of this)Reflect.apply(t,n,[e,i,this])&&r.push(e),i++;return new e(r)}map(t,n){let r=[],i=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,i,this])]),i++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i]=t,l=0,o=null!=i?i:this.at(0);for(let e of this)o=0===l&&1===t.length?e:Reflect.apply(r,this,[o,e,l,this]),l++;return o}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i]=t,l=null!=i?i:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);l=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[l,n,e,this])}return l}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=[...this.entries()];return i.splice(...n),new e(i)}slice(t,n){let r=new e,i=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(i=n-1);for(let e=t;e<=i;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,o,{writable:!0,value:void 0}),l(this,o,[...super.keys()]),p.set(this,!0)}}},8777:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},9241:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(5461),i=n(1465);function l(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);
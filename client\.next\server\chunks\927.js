"use strict";exports.id=927,exports.ids=[927],exports.modules={2499:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7612:(a,b,c)=>{c.d(b,{UC:()=>ca,In:()=>b8,q7:()=>cc,VF:()=>ce,p4:()=>cd,ZL:()=>b9,bL:()=>b5,wn:()=>cg,PP:()=>cf,l9:()=>b6,WT:()=>b7,LM:()=>cb});var d=c(43616),e=c(44145),f=c(21311),g=c(86984),h=c(88036),i=c(84677),j=c(37784),k=c(78309),l=c(56929),m=c(3568),n=c(30260),o=c(32553);let p=["top","right","bottom","left"],q=Math.min,r=Math.max,s=Math.round,t=Math.floor,u=a=>({x:a,y:a}),v={left:"right",right:"left",bottom:"top",top:"bottom"},w={start:"end",end:"start"};function x(a,b){return"function"==typeof a?a(b):a}function y(a){return a.split("-")[0]}function z(a){return a.split("-")[1]}function A(a){return"x"===a?"y":"x"}function B(a){return"y"===a?"height":"width"}let C=new Set(["top","bottom"]);function D(a){return C.has(y(a))?"y":"x"}function E(a){return a.replace(/start|end/g,a=>w[a])}let F=["left","right"],G=["right","left"],H=["top","bottom"],I=["bottom","top"];function J(a){return a.replace(/left|right|bottom|top/g,a=>v[a])}function K(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function L(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function M(a,b,c){let d,{reference:e,floating:f}=a,g=D(b),h=A(D(b)),i=B(h),j=y(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(z(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let N=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=M(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=M(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function O(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=x(b,a),o=K(n),p=h[m?"floating"===l?"reference":"floating":l],q=L(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=L(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function P(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function Q(a){return p.some(b=>a[b]>=0)}let R=new Set(["left","top"]);async function S(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=y(c),h=z(c),i="y"===D(c),j=R.has(g)?-1:1,k=f&&i?-1:1,l=x(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function T(){return"undefined"!=typeof window}function U(a){return X(a)?(a.nodeName||"").toLowerCase():"#document"}function V(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function W(a){var b;return null==(b=(X(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function X(a){return!!T()&&(a instanceof Node||a instanceof V(a).Node)}function Y(a){return!!T()&&(a instanceof Element||a instanceof V(a).Element)}function Z(a){return!!T()&&(a instanceof HTMLElement||a instanceof V(a).HTMLElement)}function $(a){return!!T()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof V(a).ShadowRoot)}let _=new Set(["inline","contents"]);function aa(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=al(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!_.has(e)}let ab=new Set(["table","td","th"]),ac=[":popover-open",":modal"];function ad(a){return ac.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let ae=["transform","translate","scale","rotate","perspective"],af=["transform","translate","scale","rotate","perspective","filter"],ag=["paint","layout","strict","content"];function ah(a){let b=ai(),c=Y(a)?al(a):a;return ae.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||af.some(a=>(c.willChange||"").includes(a))||ag.some(a=>(c.contain||"").includes(a))}function ai(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aj=new Set(["html","body","#document"]);function ak(a){return aj.has(U(a))}function al(a){return V(a).getComputedStyle(a)}function am(a){return Y(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function an(a){if("html"===U(a))return a;let b=a.assignedSlot||a.parentNode||$(a)&&a.host||W(a);return $(b)?b.host:b}function ao(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=an(b);return ak(c)?b.ownerDocument?b.ownerDocument.body:b.body:Z(c)&&aa(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=V(e);if(f){let a=ap(g);return b.concat(g,g.visualViewport||[],aa(e)?e:[],a&&c?ao(a):[])}return b.concat(e,ao(e,[],c))}function ap(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aq(a){let b=al(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=Z(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=s(c)!==f||s(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function ar(a){return Y(a)?a:a.contextElement}function as(a){let b=ar(a);if(!Z(b))return u(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aq(b),g=(f?s(c.width):c.width)/d,h=(f?s(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let at=u(0);function au(a){let b=V(a);return ai()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:at}function av(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=ar(a),h=u(1);b&&(d?Y(d)&&(h=as(d)):h=as(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===V(g))&&e)?au(g):u(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=V(g),b=d&&Y(d)?V(d):d,c=a,e=ap(c);for(;e&&d&&b!==c;){let a=as(e),b=e.getBoundingClientRect(),d=al(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=ap(c=V(e))}}return L({width:l,height:m,x:j,y:k})}function aw(a,b){let c=am(a).scrollLeft;return b?b.left+c:av(W(a)).left+c}function ax(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aw(a,d)),y:d.top+b.scrollTop}}let ay=new Set(["absolute","fixed"]);function az(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=V(a),d=W(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=ai();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=W(a),c=am(a),d=a.ownerDocument.body,e=r(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=r(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aw(a),h=-c.scrollTop;return"rtl"===al(d).direction&&(g+=r(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(W(a));else if(Y(b))d=function(a,b){let c=av(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=Z(a)?as(a):u(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=au(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return L(d)}function aA(a){return"static"===al(a).position}function aB(a,b){if(!Z(a)||"fixed"===al(a).position)return null;if(b)return b(a);let c=a.offsetParent;return W(a)===c&&(c=c.ownerDocument.body),c}function aC(a,b){var c;let d=V(a);if(ad(a))return d;if(!Z(a)){let b=an(a);for(;b&&!ak(b);){if(Y(b)&&!aA(b))return b;b=an(b)}return d}let e=aB(a,b);for(;e&&(c=e,ab.has(U(c)))&&aA(e);)e=aB(e,b);return e&&ak(e)&&aA(e)&&!ah(e)?d:e||function(a){let b=an(a);for(;Z(b)&&!ak(b);){if(ah(b))return b;if(ad(b))break;b=an(b)}return null}(a)||d}let aD=async function(a){let b=this.getOffsetParent||aC,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=Z(b),e=W(b),f="fixed"===c,g=av(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=u(0);if(d||!d&&!f)if(("body"!==U(b)||aa(e))&&(h=am(b)),d){let a=av(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aw(e));f&&!d&&e&&(i.x=aw(e));let j=!e||d||f?u(0):ax(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},aE={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=W(d),h=!!b&&ad(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=u(1),k=u(0),l=Z(d);if((l||!l&&!f)&&(("body"!==U(d)||aa(g))&&(i=am(d)),Z(d))){let a=av(d);j=as(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?u(0):ax(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:W,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?ad(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=ao(a,[],!1).filter(a=>Y(a)&&"body"!==U(a)),e=null,f="fixed"===al(a).position,g=f?an(a):a;for(;Y(g)&&!ak(g);){let b=al(g),c=ah(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&ay.has(e.position)||aa(g)&&!c&&function a(b,c){let d=an(b);return!(d===c||!Y(d)||ak(d))&&("fixed"===al(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=an(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=az(b,c,e);return a.top=r(d.top,a.top),a.right=q(d.right,a.right),a.bottom=q(d.bottom,a.bottom),a.left=r(d.left,a.left),a},az(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:aC,getElementRects:aD,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aq(a);return{width:b,height:c}},getScale:as,isElement:Y,isRTL:function(a){return"rtl"===al(a).direction}};function aF(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let aG=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=x(a,b)||{};if(null==j)return{};let l=K(k),m={x:c,y:d},n=A(D(e)),o=B(n),p=await g.getDimensions(j),s="y"===n,t=s?"clientHeight":"clientWidth",u=f.reference[o]+f.reference[n]-m[n]-f.floating[o],v=m[n]-f.reference[n],w=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),y=w?w[t]:0;y&&await (null==g.isElement?void 0:g.isElement(w))||(y=h.floating[t]||f.floating[o]);let C=y/2-p[o]/2-1,E=q(l[s?"top":"left"],C),F=q(l[s?"bottom":"right"],C),G=y-p[o]-F,H=y/2-p[o]/2+(u/2-v/2),I=r(E,q(H,G)),J=!i.arrow&&null!=z(e)&&H!==I&&f.reference[o]/2-(H<E?E:F)-p[o]/2<0,L=J?H<E?H-E:H-G:0;return{[n]:m[n]+L,data:{[n]:I,centerOffset:H-I-L,...J&&{alignmentOffset:L}},reset:J}}});var aH="undefined"!=typeof document?d.useLayoutEffect:function(){};function aI(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!aI(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!aI(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function aJ(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aK(a,b){let c=aJ(a);return Math.round(b*c)/c}function aL(a){let b=d.useRef(a);return aH(()=>{b.current=a}),b}var aM=c(7957),aN=c(21157),aO=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,aN.jsx)(aM.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,aN.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aO.displayName="Arrow";var aP=c(24452),aQ=c(27841),aR=c(62115),aS="Popper",[aT,aU]=(0,j.A)(aS),[aV,aW]=aT(aS),aX=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,aN.jsx)(aV,{scope:b,anchor:e,onAnchorChange:f,children:c})};aX.displayName=aS;var aY="PopperAnchor",aZ=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...f}=a,g=aW(aY,c),h=d.useRef(null),j=(0,i.s)(b,h);return d.useEffect(()=>{g.onAnchorChange(e?.current||h.current)}),e?null:(0,aN.jsx)(aM.sG.div,{...f,ref:j})});aZ.displayName=aY;var a$="PopperContent",[a_,a0]=aT(a$),a1=d.forwardRef((a,b)=>{let{__scopePopper:c,side:f="bottom",sideOffset:g=0,align:h="center",alignOffset:j=0,arrowPadding:k=0,avoidCollisions:l=!0,collisionBoundary:m=[],collisionPadding:n=0,sticky:o="partial",hideWhenDetached:p=!1,updatePositionStrategy:s="optimized",onPlaced:u,...v}=a,w=aW(a$,c),[C,K]=d.useState(null),L=(0,i.s)(b,a=>K(a)),[M,T]=d.useState(null),U=(0,aR.X)(M),V=U?.width??0,X=U?.height??0,Y="number"==typeof n?n:{top:0,right:0,bottom:0,left:0,...n},Z=Array.isArray(m)?m:[m],$=Z.length>0,_={padding:Y,boundary:Z.filter(a5),altBoundary:$},{refs:aa,floatingStyles:ab,placement:ac,isPositioned:ad,middlewareData:ae}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:f=[],platform:g,elements:{reference:h,floating:i}={},transform:j=!0,whileElementsMounted:k,open:l}=a,[m,n]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[o,p]=d.useState(f);aI(o,f)||p(f);let[q,r]=d.useState(null),[s,t]=d.useState(null),u=d.useCallback(a=>{a!==y.current&&(y.current=a,r(a))},[]),v=d.useCallback(a=>{a!==z.current&&(z.current=a,t(a))},[]),w=h||q,x=i||s,y=d.useRef(null),z=d.useRef(null),A=d.useRef(m),B=null!=k,C=aL(k),D=aL(g),E=aL(l),F=d.useCallback(()=>{if(!y.current||!z.current)return;let a={placement:b,strategy:c,middleware:o};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:aE,...c},f={...e.platform,_c:d};return N(a,b,{...e,platform:f})})(y.current,z.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!aI(A.current,b)&&(A.current=b,e.flushSync(()=>{n(b)}))})},[o,b,c,D,E]);aH(()=>{!1===l&&A.current.isPositioned&&(A.current.isPositioned=!1,n(a=>({...a,isPositioned:!1})))},[l]);let G=d.useRef(!1);aH(()=>(G.current=!0,()=>{G.current=!1}),[]),aH(()=>{if(w&&(y.current=w),x&&(z.current=x),w&&x){if(C.current)return C.current(w,x,F);F()}},[w,x,F,C,B]);let H=d.useMemo(()=>({reference:y,floating:z,setReference:u,setFloating:v}),[u,v]),I=d.useMemo(()=>({reference:w,floating:x}),[w,x]),J=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=aK(I.floating,m.x),d=aK(I.floating,m.y);return j?{...a,transform:"translate("+b+"px, "+d+"px)",...aJ(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,j,I.floating,m.x,m.y]);return d.useMemo(()=>({...m,update:F,refs:H,elements:I,floatingStyles:J}),[m,F,H,I,J])}({strategy:"fixed",placement:f+("center"!==h?"-"+h:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=ar(a),l=f||g?[...k?ao(k):[],...ao(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=W(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=t(l),p=t(e.clientWidth-(k+m)),s={rootMargin:-o+"px "+-p+"px "+-t(e.clientHeight-(l+n))+"px "+-t(k)+"px",threshold:r(0,q(1,i))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==i){if(!u)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||aF(j,a.getBoundingClientRect())||g(),u=!1}try{d=new IntersectionObserver(v,{...s,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(v,s)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?av(a):null;return j&&function b(){let d=av(a);p&&!aF(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===s}),elements:{reference:w.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await S(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,void 0]}))({mainAxis:g+X,alignmentAxis:j}),l&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=x(a,b),j={x:c,y:d},k=await O(b,i),l=D(y(e)),m=A(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=r(c,q(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=r(c,q(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,void 0]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===o?{...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=x(a,b),k={x:c,y:d},l=D(e),m=A(l),n=k[m],o=k[l],p=x(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=R.has(y(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(void 0),options:[void 0,void 0]}:void 0,..._}),l&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=x(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=y(h),v=D(k),w=y(k)===k,C=await (null==l.isRTL?void 0:l.isRTL(m.floating)),K=p||(w||!s?[J(k)]:function(a){let b=J(a);return[E(a),b,E(b)]}(k)),L="none"!==r;!p&&L&&K.push(...function(a,b,c,d){let e=z(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?G:F;return b?F:G;case"left":case"right":return b?H:I;default:return[]}}(y(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(E)))),f}(k,s,r,C));let M=[k,...K],N=await O(b,t),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&P.push(N[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=z(a),e=A(D(a)),f=B(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=J(g)),[g,J(g)]}(h,j,C);P.push(N[a[0]],N[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=M[a];if(b&&("alignment"!==o||v===D(b)||Q.every(a=>a.overflows[0]>0&&D(a.placement)===v)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=Q.filter(a=>{if(L){let b=D(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,void 0]}))({..._}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=x(a,b),m=await O(b,l),n=y(g),o=z(g),p="y"===D(g),{width:s,height:t}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let u=t-m.top-m.bottom,v=s-m.left-m.right,w=q(t-m[e],u),A=q(s-m[f],v),B=!b.middlewareData.shift,C=w,E=A;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(E=v),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(C=u),B&&!o){let a=r(m.left,0),b=r(m.right,0),c=r(m.top,0),d=r(m.bottom,0);p?E=s-2*(0!==a||0!==b?a+b:r(m.left,m.right)):C=t-2*(0!==c||0!==d?c+d:r(m.top,m.bottom))}await k({...b,availableWidth:E,availableHeight:C});let F=await i.getDimensions(j.floating);return s!==F.width||t!==F.height?{reset:{rects:!0}}:{}}}}(a),options:[a,void 0]}))({..._,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),M&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?aG({element:c.current,padding:d}).fn(b):{}:c?aG({element:c,padding:d}).fn(b):{}}}))(a),options:[a,void 0]}))({element:M,padding:k}),a6({arrowWidth:V,arrowHeight:X}),p&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=x(a,b);switch(d){case"referenceHidden":{let a=P(await O(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Q(a)}}}case"escaped":{let a=P(await O(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:Q(a)}}}default:return{}}}}}(a),options:[a,void 0]}))({strategy:"referenceHidden",..._})]}),[af,ag]=a7(ac),ah=(0,aP.c)(u);(0,aQ.N)(()=>{ad&&ah?.()},[ad,ah]);let ai=ae.arrow?.x,aj=ae.arrow?.y,ak=ae.arrow?.centerOffset!==0,[al,am]=d.useState();return(0,aQ.N)(()=>{C&&am(window.getComputedStyle(C).zIndex)},[C]),(0,aN.jsx)("div",{ref:aa.setFloating,"data-radix-popper-content-wrapper":"",style:{...ab,transform:ad?ab.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:al,"--radix-popper-transform-origin":[ae.transformOrigin?.x,ae.transformOrigin?.y].join(" "),...ae.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,aN.jsx)(a_,{scope:c,placedSide:af,onArrowChange:T,arrowX:ai,arrowY:aj,shouldHideArrow:ak,children:(0,aN.jsx)(aM.sG.div,{"data-side":af,"data-align":ag,...v,ref:L,style:{...v.style,animation:ad?void 0:"none"}})})})});a1.displayName=a$;var a2="PopperArrow",a3={top:"bottom",right:"left",bottom:"top",left:"right"},a4=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=a0(a2,c),f=a3[e.placedSide];return(0,aN.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,aN.jsx)(aO,{...d,ref:b,style:{...d.style,display:"block"}})})});function a5(a){return null!==a}a4.displayName=a2;var a6=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a7(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a7(a){let[b,c="center"]=a.split("-");return[b,c]}var a8=c(72514),a9=c(84726),ba=c(55002),bb=c(20355),bc=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});d.forwardRef((a,b)=>(0,aN.jsx)(aM.sG.span,{...a,ref:b,style:{...bc,...a.style}})).displayName="VisuallyHidden";var bd=c(15497),be=c(36273),bf=[" ","Enter","ArrowUp","ArrowDown"],bg=[" ","Enter"],bh="Select",[bi,bj,bk]=(0,h.N)(bh),[bl,bm]=(0,j.A)(bh,[bk,aU]),bn=aU(),[bo,bp]=bl(bh),[bq,br]=bl(bh),bs=a=>{let{__scopeSelect:b,children:c,open:e,defaultOpen:f,onOpenChange:g,value:h,defaultValue:i,onValueChange:j,dir:l,name:m,autoComplete:n,disabled:p,required:q,form:r}=a,s=bn(b),[t,u]=d.useState(null),[v,w]=d.useState(null),[x,y]=d.useState(!1),z=(0,k.jH)(l),[A,B]=(0,ba.i)({prop:e,defaultProp:f??!1,onChange:g,caller:bh}),[C,D]=(0,ba.i)({prop:h,defaultProp:i,onChange:j,caller:bh}),E=d.useRef(null),F=!t||r||!!t.closest("form"),[G,H]=d.useState(new Set),I=Array.from(G).map(a=>a.props.value).join(";");return(0,aN.jsx)(aX,{...s,children:(0,aN.jsxs)(bo,{required:q,scope:b,trigger:t,onTriggerChange:u,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:x,onValueNodeHasChildrenChange:y,contentId:(0,o.B)(),value:C,onValueChange:D,open:A,onOpenChange:B,dir:z,triggerPointerDownPosRef:E,disabled:p,children:[(0,aN.jsx)(bi.Provider,{scope:b,children:(0,aN.jsx)(bq,{scope:a.__scopeSelect,onNativeOptionAdd:d.useCallback(a=>{H(b=>new Set(b).add(a))},[]),onNativeOptionRemove:d.useCallback(a=>{H(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),F?(0,aN.jsxs)(b1,{"aria-hidden":!0,required:q,tabIndex:-1,name:m,autoComplete:n,value:C,onChange:a=>D(a.target.value),disabled:p,form:r,children:[void 0===C?(0,aN.jsx)("option",{value:""}):null,Array.from(G)]},I):null]})})};bs.displayName=bh;var bt="SelectTrigger",bu=d.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:e=!1,...f}=a,h=bn(c),j=bp(bt,c),k=j.disabled||e,l=(0,i.s)(b,j.onTriggerChange),m=bj(c),n=d.useRef("touch"),[o,p,q]=b3(a=>{let b=m().filter(a=>!a.disabled),c=b.find(a=>a.value===j.value),d=b4(b,a,c);void 0!==d&&j.onValueChange(d.value)}),r=a=>{k||(j.onOpenChange(!0),q()),a&&(j.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,aN.jsx)(aZ,{asChild:!0,...h,children:(0,aN.jsx)(aM.sG.button,{type:"button",role:"combobox","aria-controls":j.contentId,"aria-expanded":j.open,"aria-required":j.required,"aria-autocomplete":"none",dir:j.dir,"data-state":j.open?"open":"closed",disabled:k,"data-disabled":k?"":void 0,"data-placeholder":b2(j.value)?"":void 0,...f,ref:l,onClick:(0,g.m)(f.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&r(a)}),onPointerDown:(0,g.m)(f.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(r(a),a.preventDefault())}),onKeyDown:(0,g.m)(f.onKeyDown,a=>{let b=""!==o.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||p(a.key),(!b||" "!==a.key)&&bf.includes(a.key)&&(r(),a.preventDefault())})})})});bu.displayName=bt;var bv="SelectValue",bw=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,j=bp(bv,c),{onValueNodeHasChildrenChange:k}=j,l=void 0!==f,m=(0,i.s)(b,j.onValueNodeChange);return(0,aQ.N)(()=>{k(l)},[k,l]),(0,aN.jsx)(aM.sG.span,{...h,ref:m,style:{pointerEvents:"none"},children:b2(j.value)?(0,aN.jsx)(aN.Fragment,{children:g}):f})});bw.displayName=bv;var bx=d.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,aN.jsx)(aM.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});bx.displayName="SelectIcon";var by=a=>(0,aN.jsx)(a8.Z,{asChild:!0,...a});by.displayName="SelectPortal";var bz="SelectContent",bA=d.forwardRef((a,b)=>{let c=bp(bz,a.__scopeSelect),[f,g]=d.useState();return((0,aQ.N)(()=>{g(new DocumentFragment)},[]),c.open)?(0,aN.jsx)(bE,{...a,ref:b}):f?e.createPortal((0,aN.jsx)(bB,{scope:a.__scopeSelect,children:(0,aN.jsx)(bi.Slot,{scope:a.__scopeSelect,children:(0,aN.jsx)("div",{children:a.children})})}),f):null});bA.displayName=bz;var[bB,bC]=bl(bz),bD=(0,a9.TL)("SelectContent.RemoveScroll"),bE=d.forwardRef((a,b)=>{let{__scopeSelect:c,position:e="item-aligned",onCloseAutoFocus:f,onEscapeKeyDown:h,onPointerDownOutside:j,side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w,...x}=a,y=bp(bz,c),[z,A]=d.useState(null),[B,C]=d.useState(null),D=(0,i.s)(b,a=>A(a)),[E,F]=d.useState(null),[G,H]=d.useState(null),I=bj(c),[J,K]=d.useState(!1),L=d.useRef(!1);d.useEffect(()=>{if(z)return(0,bd.Eq)(z)},[z]),(0,m.Oh)();let M=d.useCallback(a=>{let[b,...c]=I().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&B&&(B.scrollTop=0),c===d&&B&&(B.scrollTop=B.scrollHeight),c?.focus(),document.activeElement!==e))return},[I,B]),N=d.useCallback(()=>M([E,z]),[M,E,z]);d.useEffect(()=>{J&&N()},[J,N]);let{onOpenChange:O,triggerPointerDownPosRef:P}=y;d.useEffect(()=>{if(z){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(P.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(P.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():z.contains(c.target)||O(!1),document.removeEventListener("pointermove",b),P.current=null};return null!==P.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[z,O,P]),d.useEffect(()=>{let a=()=>O(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[O]);let[Q,R]=b3(a=>{let b=I().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=b4(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),S=d.useCallback((a,b,c)=>{let d=!L.current&&!c;(void 0!==y.value&&y.value===b||d)&&(F(a),d&&(L.current=!0))},[y.value]),T=d.useCallback(()=>z?.focus(),[z]),U=d.useCallback((a,b,c)=>{let d=!L.current&&!c;(void 0!==y.value&&y.value===b||d)&&H(a)},[y.value]),V="popper"===e?bG:bF,W=V===bG?{side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w}:{};return(0,aN.jsx)(bB,{scope:c,content:z,viewport:B,onViewportChange:C,itemRefCallback:S,selectedItem:E,onItemLeave:T,itemTextRefCallback:U,focusSelectedItem:N,selectedItemText:G,position:e,isPositioned:J,searchRef:Q,children:(0,aN.jsx)(be.A,{as:bD,allowPinchZoom:!0,children:(0,aN.jsx)(n.n,{asChild:!0,trapped:y.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,g.m)(f,a=>{y.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,aN.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:h,onPointerDownOutside:j,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:(0,aN.jsx)(V,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:a=>a.preventDefault(),...x,...W,onPlaced:()=>K(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:(0,g.m)(x.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||R(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=I().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>M(b)),a.preventDefault()}})})})})})})});bE.displayName="SelectContentImpl";var bF=d.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:e,...g}=a,h=bp(bz,c),j=bC(bz,c),[k,l]=d.useState(null),[m,n]=d.useState(null),o=(0,i.s)(b,a=>n(a)),p=bj(c),q=d.useRef(!1),r=d.useRef(!0),{viewport:s,selectedItem:t,selectedItemText:u,focusSelectedItem:v}=j,w=d.useCallback(()=>{if(h.trigger&&h.valueNode&&k&&m&&s&&t&&u){let a=h.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=h.valueNode.getBoundingClientRect(),d=u.getBoundingClientRect();if("rtl"!==h.dir){let e=d.left-b.left,g=c.left-e,h=a.left-g,i=a.width+h,j=Math.max(i,b.width),l=window.innerWidth-10,m=(0,f.q)(g,[10,Math.max(10,l-j)]);k.style.minWidth=i+"px",k.style.left=m+"px"}else{let e=b.right-d.right,g=window.innerWidth-c.right-e,h=window.innerWidth-a.right-g,i=a.width+h,j=Math.max(i,b.width),l=window.innerWidth-10,m=(0,f.q)(g,[10,Math.max(10,l-j)]);k.style.minWidth=i+"px",k.style.right=m+"px"}let g=p(),i=window.innerHeight-20,j=s.scrollHeight,l=window.getComputedStyle(m),n=parseInt(l.borderTopWidth,10),o=parseInt(l.paddingTop,10),r=parseInt(l.borderBottomWidth,10),v=n+o+j+parseInt(l.paddingBottom,10)+r,w=Math.min(5*t.offsetHeight,v),x=window.getComputedStyle(s),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=t.offsetHeight/2,C=n+o+(t.offsetTop+B);if(C<=A){let a=g.length>0&&t===g[g.length-1].ref.current;k.style.bottom="0px";let b=Math.max(i-A,B+(a?z:0)+(m.clientHeight-s.offsetTop-s.offsetHeight)+r);k.style.height=C+b+"px"}else{let a=g.length>0&&t===g[0].ref.current;k.style.top="0px";let b=Math.max(A,n+s.offsetTop+(a?y:0)+B);k.style.height=b+(v-C)+"px",s.scrollTop=C-A+s.offsetTop}k.style.margin="10px 0",k.style.minHeight=w+"px",k.style.maxHeight=i+"px",e?.(),requestAnimationFrame(()=>q.current=!0)}},[p,h.trigger,h.valueNode,k,m,s,t,u,h.dir,e]);(0,aQ.N)(()=>w(),[w]);let[x,y]=d.useState();(0,aQ.N)(()=>{m&&y(window.getComputedStyle(m).zIndex)},[m]);let z=d.useCallback(a=>{a&&!0===r.current&&(w(),v?.(),r.current=!1)},[w,v]);return(0,aN.jsx)(bH,{scope:c,contentWrapper:k,shouldExpandOnScrollRef:q,onScrollButtonChange:z,children:(0,aN.jsx)("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:x},children:(0,aN.jsx)(aM.sG.div,{...g,ref:o,style:{boxSizing:"border-box",maxHeight:"100%",...g.style}})})})});bF.displayName="SelectItemAlignedPosition";var bG=d.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=bn(c);return(0,aN.jsx)(a1,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});bG.displayName="SelectPopperPosition";var[bH,bI]=bl(bz,{}),bJ="SelectViewport",bK=d.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:e,...f}=a,h=bC(bJ,c),j=bI(bJ,c),k=(0,i.s)(b,h.onViewportChange),l=d.useRef(0);return(0,aN.jsxs)(aN.Fragment,{children:[(0,aN.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:e}),(0,aN.jsx)(bi.Slot,{scope:c,children:(0,aN.jsx)(aM.sG.div,{"data-radix-select-viewport":"",role:"presentation",...f,ref:k,style:{position:"relative",flex:1,overflow:"hidden auto",...f.style},onScroll:(0,g.m)(f.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=j;if(d?.current&&c){let a=Math.abs(l.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}l.current=b.scrollTop})})})]})});bK.displayName=bJ;var bL="SelectGroup",[bM,bN]=bl(bL);d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=(0,o.B)();return(0,aN.jsx)(bM,{scope:c,id:e,children:(0,aN.jsx)(aM.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=bL;var bO="SelectLabel";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=bN(bO,c);return(0,aN.jsx)(aM.sG.div,{id:e.id,...d,ref:b})}).displayName=bO;var bP="SelectItem",[bQ,bR]=bl(bP),bS=d.forwardRef((a,b)=>{let{__scopeSelect:c,value:e,disabled:f=!1,textValue:h,...j}=a,k=bp(bP,c),l=bC(bP,c),m=k.value===e,[n,p]=d.useState(h??""),[q,r]=d.useState(!1),s=(0,i.s)(b,a=>l.itemRefCallback?.(a,e,f)),t=(0,o.B)(),u=d.useRef("touch"),v=()=>{f||(k.onValueChange(e),k.onOpenChange(!1))};if(""===e)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,aN.jsx)(bQ,{scope:c,value:e,disabled:f,textId:t,isSelected:m,onItemTextChange:d.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,aN.jsx)(bi.ItemSlot,{scope:c,value:e,disabled:f,textValue:n,children:(0,aN.jsx)(aM.sG.div,{role:"option","aria-labelledby":t,"data-highlighted":q?"":void 0,"aria-selected":m&&q,"data-state":m?"checked":"unchecked","aria-disabled":f||void 0,"data-disabled":f?"":void 0,tabIndex:f?void 0:-1,...j,ref:s,onFocus:(0,g.m)(j.onFocus,()=>r(!0)),onBlur:(0,g.m)(j.onBlur,()=>r(!1)),onClick:(0,g.m)(j.onClick,()=>{"mouse"!==u.current&&v()}),onPointerUp:(0,g.m)(j.onPointerUp,()=>{"mouse"===u.current&&v()}),onPointerDown:(0,g.m)(j.onPointerDown,a=>{u.current=a.pointerType}),onPointerMove:(0,g.m)(j.onPointerMove,a=>{u.current=a.pointerType,f?l.onItemLeave?.():"mouse"===u.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,g.m)(j.onPointerLeave,a=>{a.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:(0,g.m)(j.onKeyDown,a=>{(l.searchRef?.current===""||" "!==a.key)&&(bg.includes(a.key)&&v()," "===a.key&&a.preventDefault())})})})})});bS.displayName=bP;var bT="SelectItemText",bU=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:f,style:g,...h}=a,j=bp(bT,c),k=bC(bT,c),l=bR(bT,c),m=br(bT,c),[n,o]=d.useState(null),p=(0,i.s)(b,a=>o(a),l.onItemTextChange,a=>k.itemTextRefCallback?.(a,l.value,l.disabled)),q=n?.textContent,r=d.useMemo(()=>(0,aN.jsx)("option",{value:l.value,disabled:l.disabled,children:q},l.value),[l.disabled,l.value,q]),{onNativeOptionAdd:s,onNativeOptionRemove:t}=m;return(0,aQ.N)(()=>(s(r),()=>t(r)),[s,t,r]),(0,aN.jsxs)(aN.Fragment,{children:[(0,aN.jsx)(aM.sG.span,{id:l.textId,...h,ref:p}),l.isSelected&&j.valueNode&&!j.valueNodeHasChildren?e.createPortal(h.children,j.valueNode):null]})});bU.displayName=bT;var bV="SelectItemIndicator",bW=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return bR(bV,c).isSelected?(0,aN.jsx)(aM.sG.span,{"aria-hidden":!0,...d,ref:b}):null});bW.displayName=bV;var bX="SelectScrollUpButton",bY=d.forwardRef((a,b)=>{let c=bC(bX,a.__scopeSelect),e=bI(bX,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,aQ.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){g(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,aN.jsx)(b_,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});bY.displayName=bX;var bZ="SelectScrollDownButton",b$=d.forwardRef((a,b)=>{let c=bC(bZ,a.__scopeSelect),e=bI(bZ,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,aQ.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;g(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,aN.jsx)(b_,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});b$.displayName=bZ;var b_=d.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:e,...f}=a,h=bC("SelectScrollButton",c),i=d.useRef(null),j=bj(c),k=d.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return d.useEffect(()=>()=>k(),[k]),(0,aQ.N)(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,aN.jsx)(aM.sG.div,{"aria-hidden":!0,...f,ref:b,style:{flexShrink:0,...f.style},onPointerDown:(0,g.m)(f.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(e,50))}),onPointerMove:(0,g.m)(f.onPointerMove,()=>{h.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(e,50))}),onPointerLeave:(0,g.m)(f.onPointerLeave,()=>{k()})})});d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,aN.jsx)(aM.sG.div,{"aria-hidden":!0,...d,ref:b})}).displayName="SelectSeparator";var b0="SelectArrow";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=bn(c),f=bp(b0,c),g=bC(b0,c);return f.open&&"popper"===g.position?(0,aN.jsx)(a4,{...e,...d,ref:b}):null}).displayName=b0;var b1=d.forwardRef(({__scopeSelect:a,value:b,...c},e)=>{let f=d.useRef(null),g=(0,i.s)(e,f),h=(0,bb.Z)(b);return d.useEffect(()=>{let a=f.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,aN.jsx)(aM.sG.select,{...c,style:{...bc,...c.style},ref:g,defaultValue:b})});function b2(a){return""===a||void 0===a}function b3(a){let b=(0,aP.c)(a),c=d.useRef(""),e=d.useRef(0),f=d.useCallback(a=>{let d=c.current+a;b(d),function a(b){c.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(d)},[b]),g=d.useCallback(()=>{c.current="",window.clearTimeout(e.current)},[]);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),[c,f,g]}function b4(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}b1.displayName="SelectBubbleInput";var b5=bs,b6=bu,b7=bw,b8=bx,b9=by,ca=bA,cb=bK,cc=bS,cd=bU,ce=bW,cf=bY,cg=b$},20355:(a,b,c)=>{c.d(b,{Z:()=>e});var d=c(43616);function e(a){let b=d.useRef({value:a,previous:a});return d.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}},21311:(a,b,c)=>{c.d(b,{q:()=>d});function d(a,[b,c]){return Math.min(c,Math.max(b,a))}},40812:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},40909:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(71737).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},62115:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43616),e=c(27841);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},78309:(a,b,c)=>{c.d(b,{jH:()=>f});var d=c(43616);c(21157);var e=d.createContext(void 0);function f(a){let b=d.useContext(e);return a||b||"ltr"}},88036:(a,b,c)=>{c.d(b,{N:()=>i});var d=c(43616),e=c(37784),f=c(84677),g=c(84726),h=c(21157);function i(a){let b=a+"CollectionProvider",[c,i]=(0,e.A)(b),[j,k]=c(b,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:b,children:c}=a,e=d.useRef(null),f=d.useRef(new Map).current;return(0,h.jsx)(j,{scope:b,itemMap:f,collectionRef:e,children:c})};l.displayName=b;let m=a+"CollectionSlot",n=(0,g.TL)(m),o=d.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=k(m,c),g=(0,f.s)(b,e.collectionRef);return(0,h.jsx)(n,{ref:g,children:d})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,g.TL)(p),s=d.forwardRef((a,b)=>{let{scope:c,children:e,...g}=a,i=d.useRef(null),j=(0,f.s)(b,i),l=k(p,c);return d.useEffect(()=>(l.itemMap.set(i,{ref:i,...g}),()=>void l.itemMap.delete(i))),(0,h.jsx)(r,{...{[q]:""},ref:j,children:e})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(b){let c=k(a+"CollectionConsumer",b);return d.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},i]}var j=new WeakMap;function k(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=l(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function l(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],j.set(this,!0)}set(a,b){return j.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=l(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],m=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||k[a-1]!==b||(m=!0);let c=k[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=k(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=k(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return k(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})}};
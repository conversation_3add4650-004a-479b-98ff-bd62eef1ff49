(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{44:(e,a,s)=>{Promise.resolve().then(s.bind(s,7387))},1640:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(865).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3565:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(865).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7387:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>$});var i=s(2273),t=s(5461),l=s(7320),n=s(2521),r=s(3427),d=s(5573),c=s(1101),m=s(1991),o=s(74),x=s(2814),h=s(1640),g=s(3565);let u=[{icon:d.A,value:"10,000+",label:"Pengguna Aktif"},{icon:c.A,value:"500+",label:"Kost Terdaftar"},{icon:m.A,value:"4.8",label:"Rating Rata-rata"},{icon:o.A,value:"100%",label:"Terverifikasi"}],p=[{icon:x.A,title:"Preview Dinamis",description:"Lihat detail kost dengan preview interaktif dan carousel gambar"},{icon:h.A,title:"Perbandingan Mudah",description:"Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail"},{icon:g.A,title:"Terverifikasi",description:"Semua kost telah diverifikasi untuk memastikan kualitas dan keamanan"}],j=["Jakarta Selatan","Bandung","Yogyakarta","Surabaya","Malang","Semarang"];function v(e){let{onSearch:a}=e;return(0,i.jsxs)("section",{className:"relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 hero-gradient opacity-90"}),(0,i.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,i.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,i.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}})}),(0,i.jsx)("div",{className:"relative container mx-auto px-4 py-20 lg:py-32",children:(0,i.jsxs)("div",{className:"text-center space-y-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(n.E,{variant:"secondary",className:"bg-white/20 text-white border-white/30",children:"\uD83C\uDFE0 Platform Pencarian Kost Terdepan"}),(0,i.jsxs)("h1",{className:"text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight",children:["Temukan Kost",(0,i.jsx)("br",{}),(0,i.jsx)("span",{className:"text-yellow-300",children:"Impian Anda"})]}),(0,i.jsx)("p",{className:"text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed",children:"Platform inovatif dengan preview dinamis dan fitur perbandingan interaktif untuk membantu Anda menemukan tempat tinggal yang sempurna."})]}),(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsx)(r.I,{onSearch:a,placeholder:"Cari berdasarkan lokasi, nama kost, atau fasilitas...",className:"bg-white/95 backdrop-blur-sm rounded-2xl p-2 shadow-2xl"})}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("p",{className:"text-white/80 text-sm font-medium",children:"Lokasi Populer:"}),(0,i.jsx)("div",{className:"flex flex-wrap justify-center gap-2",children:j.map(e=>(0,i.jsxs)(l.$,{variant:"outline",size:"sm",className:"bg-white/10 border-white/30 text-white hover:bg-white/20 hover:text-white transition-all duration-300",onClick:()=>{a("",{location:e,type:"semua",priceRange:[5e5,5e6],facilities:[],sortBy:"relevance"})},children:[(0,i.jsx)(c.A,{className:"h-3 w-3 mr-1"}),e]},e))})]}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto pt-8",children:u.map((e,a)=>(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mb-2",children:(0,i.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,i.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-white",children:e.value}),(0,i.jsx)("div",{className:"text-sm text-white/80",children:e.label})]},a))})]})}),(0,i.jsx)("div",{className:"relative bg-white/5 backdrop-blur-sm border-t border-white/10",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Mengapa Memilih KostHub?"}),(0,i.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Fitur-fitur inovatif yang memudahkan pencarian kost impian Anda"})]}),(0,i.jsx)("div",{className:"grid md:grid-cols-3 gap-8 max-w-5xl mx-auto",children:p.map((e,a)=>(0,i.jsxs)("div",{className:"text-center space-y-4 group",children:[(0,i.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 group-hover:bg-white/20 transition-colors duration-300",children:(0,i.jsx)(e.icon,{className:"h-8 w-8 text-white"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-white",children:e.title}),(0,i.jsx)("p",{className:"text-white/80 leading-relaxed",children:e.description})]},a))}),(0,i.jsx)("div",{className:"text-center mt-12",children:(0,i.jsx)(l.$,{size:"lg",className:"bg-white text-primary hover:bg-white/90 font-semibold px-8 py-3 text-lg",onClick:()=>{var e;null==(e=document.getElementById("kost-listings"))||e.scrollIntoView({behavior:"smooth"})},children:"Jelajahi Kost Sekarang"})})]})})]})}var b=s(8919),N=s(9393);function f(){return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,i.jsx)(N.E,{className:"h-8 w-3/4"}),(0,i.jsx)(N.E,{className:"h-4 w-1/2"}),(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)(N.E,{className:"h-4 w-24"}),(0,i.jsx)(N.E,{className:"h-6 w-20"})]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(N.E,{className:"h-9 w-20"}),(0,i.jsx)(N.E,{className:"h-9 w-20"})]})]})}),(0,i.jsx)(N.E,{className:"aspect-[16/9] w-full rounded-lg"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg",children:Array.from({length:4}).map((e,a)=>(0,i.jsx)(N.E,{className:"h-9 flex-1"},a))}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(N.E,{className:"h-4 w-full"}),(0,i.jsx)(N.E,{className:"h-4 w-3/4"}),(0,i.jsx)(N.E,{className:"h-4 w-1/2"})]})]})]})}s(298);var k=s(8311),w=s(7466),y=s(3293),A=s(5383),E=s(6309);let C=(0,t.lazy)(()=>Promise.all([s.e(256),s.e(172)]).then(s.bind(s,7172)).then(e=>({default:e.KostPreviewDialog}))),K=(0,t.lazy)(()=>Promise.all([s.e(450),s.e(914),s.e(352)]).then(s.bind(s,914)).then(e=>({default:e.ComparisonDialog}))),P=[{id:"1",title:"Kost Melati Residence",location:"Kemang, Jakarta Selatan",price:25e5,rating:4.8,reviewCount:124,images:[k.Y$.kost.room1,k.Y$.kost.interior1,k.Y$.kost.interior2],facilities:["WiFi","Parkir","Dapur","Listrik","Air","Keamanan"],type:"putri",available:3,description:"Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.",isWishlisted:!1},{id:"2",title:"Griya Mahasiswa Bandung",location:"Dago, Bandung",price:18e5,rating:4.6,reviewCount:89,images:[k.Y$.kost.room2,k.Y$.kost.interior3],facilities:["WiFi","Dapur","Listrik","Air","Ruang Tamu"],type:"putra",available:5,description:"Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.",isWishlisted:!0},{id:"3",title:"Kost Harmoni Yogya",location:"Malioboro, Yogyakarta",price:15e5,rating:4.7,reviewCount:156,images:[k.Y$.kost.room3],facilities:["WiFi","Parkir","Listrik","Air","Keamanan","AC"],type:"campur",available:2,description:"Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.",isWishlisted:!1}],S=[{name:"Sarah Putri",location:"Jakarta",rating:5,comment:"Platform yang sangat membantu! Fitur perbandingan kostnya sangat berguna untuk memilih kost yang tepat.",avatar:k.Y$.avatars.female1},{name:"Ahmad Rizki",location:"Bandung",rating:5,comment:"Preview dinamis membuat saya bisa melihat detail kost dengan jelas sebelum memutuskan. Recommended!",avatar:k.Y$.avatars.male1},{name:"Dina Maharani",location:"Yogyakarta",rating:4,comment:"Pencarian kost jadi lebih mudah dan cepat. Interface yang user-friendly dan informatif.",avatar:k.Y$.avatars.female2}];function $(){let[e,a]=(0,t.useState)(null),[s,r]=(0,t.useState)(!1),[x,g]=(0,t.useState)([]),[u,p]=(0,t.useState)(!1),[j,N]=(0,t.useState)(["2"]),k=e=>{a(e),r(!0)},$=e=>{N(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},Y=e=>{let a=P.find(a=>a.id===e);a&&g(s=>s.some(a=>a.id===e)?s.filter(a=>a.id!==e):s.length<3?[...s,a]:[a,...s.slice(1)])};return(0,i.jsxs)("div",{className:"min-h-screen",children:[(0,i.jsx)(v,{onSearch:(e,a)=>{console.log("Search:",e,a)}}),(0,i.jsx)("section",{id:"kost-listings",className:"py-16 bg-background",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsxs)(n.E,{variant:"outline",className:"mb-4",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Kost Terpopuler"]}),(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Kost Pilihan Terbaik"}),(0,i.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Temukan kost berkualitas tinggi yang telah dipilih khusus untuk Anda"})]}),(0,i.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12",children:P.map(e=>(0,i.jsx)(b.y,{kost:{...e,isWishlisted:j.includes(e.id)},onPreview:k,onWishlist:$,onCompare:Y,isComparing:x.some(a=>a.id===e.id)},e.id))}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsxs)(l.$,{size:"lg",variant:"outline",children:["Lihat Semua Kost",(0,i.jsx)(A.A,{className:"h-4 w-4 ml-2"})]})})]})}),(0,i.jsx)(y.w,{}),(0,i.jsx)("section",{className:"py-16 bg-muted/30",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsxs)(n.E,{variant:"outline",className:"mb-4",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Testimoni Pengguna"]}),(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Apa Kata Mereka?"}),(0,i.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Pengalaman nyata dari pengguna KostHub yang telah menemukan kost impian mereka"})]}),(0,i.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:S.map((e,a)=>(0,i.jsxs)("div",{className:"bg-card p-6 rounded-lg border",children:[(0,i.jsx)("div",{className:"flex items-center gap-1 mb-4",children:Array.from({length:e.rating}).map((e,a)=>(0,i.jsx)(m.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"},a))}),(0,i.jsxs)("p",{className:"text-muted-foreground mb-4 leading-relaxed",children:['"',e.comment,'"']}),(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"relative w-10 h-10 rounded-full overflow-hidden",children:(0,i.jsx)(w.default,{src:e.avatar,alt:"".concat(e.name," avatar"),fill:!0,className:"object-cover",sizes:"40px"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.name}),(0,i.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,i.jsx)(c.A,{className:"h-3 w-3"}),e.location]})]})]})]},a))})]})}),(0,i.jsx)(y.w,{}),(0,i.jsx)("section",{className:"py-16 bg-background",children:(0,i.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,i.jsxs)("div",{className:"max-w-3xl mx-auto space-y-6",children:[(0,i.jsxs)(n.E,{variant:"outline",className:"mb-4",children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Bergabung Sekarang"]}),(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold",children:"Siap Menemukan Kost Impian Anda?"}),(0,i.jsx)("p",{className:"text-muted-foreground text-lg",children:"Bergabunglah dengan ribuan pengguna yang telah menemukan tempat tinggal ideal mereka melalui KostHub"}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsxs)(l.$,{size:"lg",className:"px-8",children:[(0,i.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Mulai Pencarian"]}),(0,i.jsx)(l.$,{size:"lg",variant:"outline",className:"px-8",children:"Daftarkan Kost Anda"})]})]})})}),x.length>0&&(0,i.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,i.jsxs)(l.$,{onClick:()=>p(!0),className:"rounded-full shadow-lg",size:"lg",children:["Bandingkan (",x.length,")"]})}),(0,i.jsxs)(t.Suspense,{fallback:(0,i.jsx)(f,{}),children:[(0,i.jsx)(C,{kost:e,isOpen:s,onClose:()=>r(!1),onWishlist:$,onCompare:Y,isComparing:!!e&&x.some(a=>a.id===e.id)}),(0,i.jsx)(K,{kosts:x,isOpen:u,onClose:()=>p(!1),onRemoveFromComparison:e=>{g(a=>a.filter(a=>a.id!==e))}})]})]})}}},e=>{e.O(0,[335,482,739,644,402,214,305,970,804,358],()=>e(e.s=44)),_N_E=e.O()}]);
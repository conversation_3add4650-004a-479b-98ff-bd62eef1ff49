"use strict";exports.id=409,exports.ids=[409],exports.modules={77409:(a,b,c)=>{c.r(b),c.d(b,{ComparisonDialog:()=>E});var d=c(21157),e=c(43616),f=c.n(e),g=c(31962),h=c(90574),i=c(56357),j=c(52945),k=c(54606),l=c(89369);function m({className:a,children:b,...c}){return(0,d.jsxs)(k.bL,{"data-slot":"scroll-area",className:(0,l.cn)("relative",a),...c,children:[(0,d.jsx)(k.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:b}),(0,d.jsx)(n,{}),(0,d.jsx)(k.<PERSON>,{})]})}function n({className:a,orientation:b="vertical",...c}){return(0,d.jsx)(k.VM,{"data-slot":"scroll-area-scrollbar",orientation:b,className:(0,l.cn)("flex touch-none p-px transition-colors select-none","vertical"===b&&"h-full w-2.5 border-l border-l-transparent","horizontal"===b&&"h-2.5 flex-col border-t border-t-transparent",a),...c,children:(0,d.jsx)(k.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var o=c(89122),p=c(45817),q=c(71960),r=c(8932),s=c(18080),t=c(88358),u=c(68307),v=c(44939),w=c(16635),x=c(38163),y=c(2499),z=c(95763),A=c(96220),B=c(70696);let C={wifi:o.A,parkir:p.A,dapur:q.A,listrik:r.A,air:s.A,keamanan:t.A,"ruang tamu":u.A},D=["WiFi","Parkir","Dapur","Listrik","Air","Keamanan","Ruang Tamu","AC","Kasur","Lemari"];function E({kosts:a,isOpen:b,onClose:c,onRemoveFromComparison:e}){let k=a=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(a),l=a=>{switch(a){case"putra":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"putri":return"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";case"campur":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}};return 0===a.length?(0,d.jsx)(g.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(g.Cf,{className:"max-w-md",children:[(0,d.jsx)(g.c7,{children:(0,d.jsx)(g.L3,{children:"Perbandingan Kost"})}),(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Belum ada kost yang dipilih untuk dibandingkan."}),(0,d.jsx)(h.$,{onClick:c,children:"Tutup"})]})]})}):(0,d.jsx)(g.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(g.Cf,{className:"max-w-6xl max-h-[90vh] overflow-hidden",children:[(0,d.jsx)(g.c7,{children:(0,d.jsxs)(g.L3,{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{children:["Perbandingan Kost (",a.length,")"]}),(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:c,children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})]})}),(0,d.jsx)(m,{className:"h-[calc(90vh-120px)]",children:(0,d.jsxs)("div",{className:"comparison-table",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:a.map(a=>(0,d.jsxs)("div",{className:"relative bg-card border rounded-lg overflow-hidden",children:[(0,d.jsx)(h.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-white/90 hover:bg-white",onClick:()=>e(a.id),children:(0,d.jsx)(v.A,{className:"h-4 w-4"})}),(0,d.jsx)("div",{className:"relative aspect-[4/3]",children:(0,d.jsx)(B.default,{src:a.images[0]||A.wf,alt:a.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-2",children:[(0,d.jsx)(w.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"line-clamp-1",children:a.location})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,d.jsx)("span",{className:"font-medium text-sm",children:a.rating})]}),(0,d.jsx)(i.E,{className:l(a.type),children:a.type})]}),(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:k(a.price)}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"per bulan"})]})]},a.id))}),(0,d.jsx)(j.w,{className:"my-6"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-lg mb-4",children:"Informasi Dasar"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsx)("div",{className:"font-medium text-muted-foreground",children:"Harga per Bulan"}),a.map(a=>(0,d.jsx)("div",{className:"font-semibold text-primary",children:k(a.price)},`price-${a.id}`))]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsx)("div",{className:"font-medium text-muted-foreground",children:"Rating"}),a.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,d.jsx)("span",{className:"font-medium",children:a.rating}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",a.reviewCount,")"]})]},`rating-${a.id}`))]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsx)("div",{className:"font-medium text-muted-foreground",children:"Kamar Tersedia"}),a.map(a=>(0,d.jsxs)("div",{children:[a.available," kamar"]},`available-${a.id}`))]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsx)("div",{className:"font-medium text-muted-foreground",children:"Tipe Kost"}),a.map(a=>(0,d.jsx)("div",{children:(0,d.jsxs)(i.E,{className:l(a.type),children:["Kost ",a.type.charAt(0).toUpperCase()+a.type.slice(1)]})},`type-${a.id}`))]})]})]}),(0,d.jsx)(j.w,{}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-lg mb-4",children:"Perbandingan Fasilitas"}),(0,d.jsx)("div",{className:"space-y-3",children:D.map(b=>(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-medium text-muted-foreground",children:[C[b.toLowerCase()]&&f().createElement(C[b.toLowerCase()],{className:"h-4 w-4"}),b]}),a.map(a=>(0,d.jsx)("div",{className:"flex items-center",children:a.facilities.some(a=>a.toLowerCase()===b.toLowerCase())?(0,d.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,d.jsx)(y.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:"Tersedia"})]}):(0,d.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,d.jsx)(z.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:"Tidak tersedia"})]})},`${b}-${a.id}`))]},b))})]}),(0,d.jsx)(j.w,{}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsx)("div",{className:"font-medium text-muted-foreground",children:"Aksi"}),a.map(a=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.$,{className:"w-full",size:"sm",children:"Lihat Detail"}),(0,d.jsx)(h.$,{variant:"outline",className:"w-full",size:"sm",children:"Hubungi Pemilik"})]},`action-${a.id}`))]})]})]})})]})})}}};
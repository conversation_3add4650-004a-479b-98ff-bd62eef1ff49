"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[914],{914:(e,s,a)=>{a.r(s),a.d(s,{ComparisonDialog:()=>K});var l=a(2273),i=a(5461),r=a(7352),t=a(7320),c=a(2521),d=a(3293),n=a(2893),m=a(1415);function o(e){let{className:s,children:a,...i}=e;return(0,l.jsxs)(n.bL,{"data-slot":"scroll-area",className:(0,m.cn)("relative",s),...i,children:[(0,l.jsx)(n.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:a}),(0,l.jsx)(x,{}),(0,l.jsx)(n.OK,{})]})}function x(e){let{className:s,orientation:a="vertical",...i}=e;return(0,l.jsx)(n.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,m.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",s),...i,children:(0,l.jsx)(n.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var h=a(1821),g=a(87),u=a(8140),p=a(2814),j=a(428),N=a(74),f=a(5573),v=a(9889),b=a(1101),w=a(1991),k=a(4137),y=a(5185),A=a(8311),C=a(7466);let L={wifi:h.A,parkir:g.A,dapur:u.A,listrik:p.A,air:j.A,keamanan:N.A,"ruang tamu":f.A},z=["WiFi","Parkir","Dapur","Listrik","Air","Keamanan","Ruang Tamu","AC","Kasur","Lemari"];function K(e){let{kosts:s,isOpen:a,onClose:n,onRemoveFromComparison:m}=e,x=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),h=e=>{switch(e){case"putra":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"putri":return"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";case"campur":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}};return 0===s.length?(0,l.jsx)(r.lG,{open:a,onOpenChange:n,children:(0,l.jsxs)(r.Cf,{className:"max-w-md",children:[(0,l.jsx)(r.c7,{children:(0,l.jsx)(r.L3,{children:"Perbandingan Kost"})}),(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"Belum ada kost yang dipilih untuk dibandingkan."}),(0,l.jsx)(t.$,{onClick:n,children:"Tutup"})]})]})}):(0,l.jsx)(r.lG,{open:a,onOpenChange:n,children:(0,l.jsxs)(r.Cf,{className:"max-w-6xl max-h-[90vh] overflow-hidden",children:[(0,l.jsx)(r.c7,{children:(0,l.jsxs)(r.L3,{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{children:["Perbandingan Kost (",s.length,")"]}),(0,l.jsx)(t.$,{variant:"ghost",size:"sm",onClick:n,children:(0,l.jsx)(v.A,{className:"h-4 w-4"})})]})}),(0,l.jsx)(o,{className:"h-[calc(90vh-120px)]",children:(0,l.jsxs)("div",{className:"comparison-table",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:s.map(e=>(0,l.jsxs)("div",{className:"relative bg-card border rounded-lg overflow-hidden",children:[(0,l.jsx)(t.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-white/90 hover:bg-white",onClick:()=>m(e.id),children:(0,l.jsx)(v.A,{className:"h-4 w-4"})}),(0,l.jsx)("div",{className:"relative aspect-[4/3]",children:(0,l.jsx)(C.default,{src:e.images[0]||A.wf,alt:e.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:e.title}),(0,l.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-2",children:[(0,l.jsx)(b.A,{className:"h-3 w-3"}),(0,l.jsx)("span",{className:"line-clamp-1",children:e.location})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(w.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,l.jsx)("span",{className:"font-medium text-sm",children:e.rating})]}),(0,l.jsx)(c.E,{className:h(e.type),children:e.type})]}),(0,l.jsx)("div",{className:"text-2xl font-bold text-primary",children:x(e.price)}),(0,l.jsx)("div",{className:"text-sm text-muted-foreground",children:"per bulan"})]})]},e.id))}),(0,l.jsx)(d.w,{className:"my-6"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-lg mb-4",children:"Informasi Dasar"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)("div",{className:"font-medium text-muted-foreground",children:"Harga per Bulan"}),s.map(e=>(0,l.jsx)("div",{className:"font-semibold text-primary",children:x(e.price)},"price-".concat(e.id)))]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)("div",{className:"font-medium text-muted-foreground",children:"Rating"}),s.map(e=>(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(w.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,l.jsx)("span",{className:"font-medium",children:e.rating}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",e.reviewCount,")"]})]},"rating-".concat(e.id)))]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)("div",{className:"font-medium text-muted-foreground",children:"Kamar Tersedia"}),s.map(e=>(0,l.jsxs)("div",{children:[e.available," kamar"]},"available-".concat(e.id)))]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)("div",{className:"font-medium text-muted-foreground",children:"Tipe Kost"}),s.map(e=>(0,l.jsx)("div",{children:(0,l.jsxs)(c.E,{className:h(e.type),children:["Kost ",e.type.charAt(0).toUpperCase()+e.type.slice(1)]})},"type-".concat(e.id)))]})]})]}),(0,l.jsx)(d.w,{}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-lg mb-4",children:"Perbandingan Fasilitas"}),(0,l.jsx)("div",{className:"space-y-3",children:z.map(e=>(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 font-medium text-muted-foreground",children:[L[e.toLowerCase()]&&i.createElement(L[e.toLowerCase()],{className:"h-4 w-4"}),e]}),s.map(s=>(0,l.jsx)("div",{className:"flex items-center",children:s.facilities.some(s=>s.toLowerCase()===e.toLowerCase())?(0,l.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,l.jsx)(k.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"text-sm",children:"Tersedia"})]}):(0,l.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,l.jsx)(y.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"text-sm",children:"Tidak tersedia"})]})},"".concat(e,"-").concat(s.id)))]},e))})]}),(0,l.jsx)(d.w,{}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)("div",{className:"font-medium text-muted-foreground",children:"Aksi"}),s.map(e=>(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(t.$,{className:"w-full",size:"sm",children:"Lihat Detail"}),(0,l.jsx)(t.$,{variant:"outline",className:"w-full",size:"sm",children:"Hubungi Pemilik"})]},"action-".concat(e.id)))]})]})]})})]})})}}}]);
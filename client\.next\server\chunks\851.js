"use strict";exports.id=851,exports.ids=[851,962],exports.modules={31962:(a,b,c)=>{c.d(b,{Cf:()=>k,L3:()=>m,c7:()=>l,lG:()=>h});var d=c(21157);c(43616);var e=c(5930),f=c(44939),g=c(89369);function h({...a}){return(0,d.jsx)(e.bL,{"data-slot":"dialog",...a})}function i({...a}){return(0,d.jsx)(e.ZL,{"data-slot":"dialog-portal",...a})}function j({className:a,...b}){return(0,d.jsx)(e.hJ,{"data-slot":"dialog-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function k({className:a,children:b,showCloseButton:c=!0,...h}){return(0,d.jsxs)(i,{"data-slot":"dialog-portal",children:[(0,d.jsx)(j,{}),(0,d.jsxs)(e.UC,{"data-slot":"dialog-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...h,children:[b,c&&(0,d.jsxs)(e.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,d.jsx)(f.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function l({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"dialog-header",className:(0,g.cn)("flex flex-col gap-2 text-center sm:text-left",a),...b})}function m({className:a,...b}){return(0,d.jsx)(e.hE,{"data-slot":"dialog-title",className:(0,g.cn)("text-lg leading-none font-semibold",a),...b})}},95851:(a,b,c)=>{c.r(b),c.d(b,{KostPreviewDialog:()=>U});var d=c(21157),e=c(70696),f=c(31962),g=c(90574),h=c(56357),i=c(43616),j=c(27168),k=c(89369);function l({className:a,...b}){return(0,d.jsx)(j.bL,{"data-slot":"tabs",className:(0,k.cn)("flex flex-col gap-2",a),...b})}function m({className:a,...b}){return(0,d.jsx)(j.B8,{"data-slot":"tabs-list",className:(0,k.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...b})}function n({className:a,...b}){return(0,d.jsx)(j.l9,{"data-slot":"tabs-trigger",className:(0,k.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...b})}function o({className:a,...b}){return(0,d.jsx)(j.UC,{"data-slot":"tabs-content",className:(0,k.cn)("flex-1 outline-none",a),...b})}var p=c(18715),q=c(91466),r=c(39401);let s=i.createContext(null);function t(){let a=i.useContext(s);if(!a)throw Error("useCarousel must be used within a <Carousel />");return a}function u({orientation:a="horizontal",opts:b,setApi:c,plugins:e,className:f,children:g,...h}){let[j,l]=(0,p.A)({...b,axis:"horizontal"===a?"x":"y"},e),[m,n]=i.useState(!1),[o,q]=i.useState(!1),r=i.useCallback(a=>{a&&(n(a.canScrollPrev()),q(a.canScrollNext()))},[]),t=i.useCallback(()=>{l?.scrollPrev()},[l]),u=i.useCallback(()=>{l?.scrollNext()},[l]),v=i.useCallback(a=>{"ArrowLeft"===a.key?(a.preventDefault(),t()):"ArrowRight"===a.key&&(a.preventDefault(),u())},[t,u]);return i.useEffect(()=>{l&&c&&c(l)},[l,c]),i.useEffect(()=>{if(l)return r(l),l.on("reInit",r),l.on("select",r),()=>{l?.off("select",r)}},[l,r]),(0,d.jsx)(s.Provider,{value:{carouselRef:j,api:l,opts:b,orientation:a||(b?.axis==="y"?"vertical":"horizontal"),scrollPrev:t,scrollNext:u,canScrollPrev:m,canScrollNext:o},children:(0,d.jsx)("div",{onKeyDownCapture:v,className:(0,k.cn)("relative",f),role:"region","aria-roledescription":"carousel","data-slot":"carousel",...h,children:g})})}function v({className:a,...b}){let{carouselRef:c,orientation:e}=t();return(0,d.jsx)("div",{ref:c,className:"overflow-hidden","data-slot":"carousel-content",children:(0,d.jsx)("div",{className:(0,k.cn)("flex","horizontal"===e?"-ml-4":"-mt-4 flex-col",a),...b})})}function w({className:a,...b}){let{orientation:c}=t();return(0,d.jsx)("div",{role:"group","aria-roledescription":"slide","data-slot":"carousel-item",className:(0,k.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===c?"pl-4":"pt-4",a),...b})}function x({className:a,variant:b="outline",size:c="icon",...e}){let{orientation:f,scrollPrev:h,canScrollPrev:i}=t();return(0,d.jsxs)(g.$,{"data-slot":"carousel-previous",variant:b,size:c,className:(0,k.cn)("absolute size-8 rounded-full","horizontal"===f?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!i,onClick:h,...e,children:[(0,d.jsx)(q.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function y({className:a,variant:b="outline",size:c="icon",...e}){let{orientation:f,scrollNext:h,canScrollNext:i}=t();return(0,d.jsxs)(g.$,{"data-slot":"carousel-next",variant:b,size:c,className:(0,k.cn)("absolute size-8 rounded-full","horizontal"===f?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!i,onClick:h,...e,children:[(0,d.jsx)(r.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Next slide"})]})}var z=c(89122),A=c(45817),B=c(71960),C=c(8932),D=c(18080),E=c(88358),F=c(68307),G=c(95540),H=c(14063),I=c(16635),J=c(38163),K=c(49679),L=c(26339),M=c(11561),N=c(71223),O=c(28993),P=c(11041),Q=c(96220);let R={wifi:z.A,parkir:A.A,dapur:B.A,listrik:C.A,air:D.A,keamanan:E.A,"ruang tamu":F.A,ac:C.A,kasur:G.A,lemari:H.A},S=[{id:"1",user:"Andi Pratama",rating:5,comment:"Kost yang sangat nyaman dan bersih. Pemilik ramah dan fasilitas lengkap.",date:"2024-01-15",avatar:Q.Y$.avatars.male2},{id:"2",user:"Sari Dewi",rating:4,comment:"Lokasi strategis dekat kampus. WiFi cepat dan kamar luas.",date:"2024-01-10",avatar:Q.Y$.avatars.female3},{id:"3",user:"Budi Santoso",rating:5,comment:"Pelayanan excellent! Kost bersih dan aman. Highly recommended untuk mahasiswa.",date:"2024-01-08",avatar:Q.Y$.avatars.male5}],T=["Jam malam pukul 22.00 WIB","Dilarang membawa tamu menginap","Dilarang merokok di dalam kamar","Wajib menjaga kebersihan bersama","Pembayaran dilakukan setiap tanggal 1"];function U({kost:a,isOpen:b,onClose:c,onWishlist:i,onCompare:j,isComparing:p=!1}){let q;return a?(0,d.jsx)(f.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(f.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto custom-scrollbar",children:[(0,d.jsx)(f.c7,{className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(f.L3,{className:"text-2xl font-bold",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground",children:[(0,d.jsx)(I.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.location})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(J.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,d.jsx)("span",{className:"font-medium",children:a.rating}),(0,d.jsxs)("span",{className:"text-muted-foreground",children:["(",a.reviewCount," ulasan)"]})]}),(0,d.jsxs)(h.E,{className:(a=>{switch(a){case"putra":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"putri":return"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";case"campur":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}})(a.type),children:["Kost ",a.type.charAt(0).toUpperCase()+a.type.slice(1)]})]})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>i?.(a.id),children:[(0,d.jsx)(K.A,{className:(0,k.cn)("h-4 w-4 mr-2",a.isWishlisted?"fill-red-500 text-red-500":"")}),a.isWishlisted?"Tersimpan":"Simpan"]}),(0,d.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"Bagikan"]})]})]})}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"relative",children:(0,d.jsxs)(u,{className:"w-full",children:[(0,d.jsx)(v,{children:a.images.map((b,c)=>(0,d.jsx)(w,{children:(0,d.jsx)("div",{className:"relative aspect-[16/9] overflow-hidden rounded-lg",children:(0,d.jsx)(e.default,{src:b,alt:`${a.title} - Gambar ${c+1}`,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 800px"})})},c))}),(0,d.jsx)(x,{className:"left-4"}),(0,d.jsx)(y,{className:"right-4"})]})}),(0,d.jsxs)(l,{defaultValue:"overview",className:"w-full",children:[(0,d.jsxs)(m,{className:"grid w-full grid-cols-4",children:[(0,d.jsx)(n,{value:"overview",children:"Ringkasan"}),(0,d.jsx)(n,{value:"facilities",children:"Fasilitas"}),(0,d.jsx)(n,{value:"reviews",children:"Ulasan"}),(0,d.jsx)(n,{value:"rules",children:"Peraturan"})]}),(0,d.jsx)(o,{value:"overview",className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Deskripsi"}),(0,d.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:a.description})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Detail Kost"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(G.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("span",{className:"text-sm",children:[a.available," kamar tersedia"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(H.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("span",{className:"text-sm",children:["Tipe: Kost ",a.type]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(M.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("span",{className:"text-sm",children:"Pembayaran bulanan"})]})]})]})]}),(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)("div",{className:"bg-muted p-4 rounded-lg",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-primary mb-1",children:(q=a.price,new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(q))}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground mb-4",children:"per bulan"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(g.$,{className:"w-full",size:"lg",children:[(0,d.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Hubungi Pemilik"]}),(0,d.jsxs)(g.$,{variant:"outline",className:"w-full",size:"lg",children:[(0,d.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Chat WhatsApp"]}),j&&(0,d.jsx)(g.$,{variant:p?"default":"outline",className:"w-full",size:"lg",onClick:()=>j(a.id),children:p?"Terpilih untuk Perbandingan":"Bandingkan Kost"})]})]})})]})}),(0,d.jsx)(o,{value:"facilities",className:"space-y-4",children:(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:a.facilities.map(a=>{let b=R[a.toLowerCase()]||E.A;return(0,d.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted rounded-lg",children:[(0,d.jsx)(b,{className:"h-5 w-5 text-primary"}),(0,d.jsx)("span",{className:"font-medium",children:a})]},a)})})}),(0,d.jsx)(o,{value:"reviews",className:"space-y-4",children:(0,d.jsx)("div",{className:"space-y-4",children:S.map(a=>(0,d.jsx)("div",{className:"border rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"relative w-10 h-10 rounded-full overflow-hidden flex-shrink-0",children:(0,d.jsx)(e.default,{src:a.avatar,alt:`${a.user} avatar`,fill:!0,className:"object-cover",sizes:"40px"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,d.jsx)("span",{className:"font-medium",children:a.user}),(0,d.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:a.rating}).map((a,b)=>(0,d.jsx)(J.A,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"},b))})]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:a.comment}),(0,d.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,d.jsx)(P.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:new Date(a.date).toLocaleDateString("id-ID")})]})]})]})},a.id))})}),(0,d.jsx)(o,{value:"rules",className:"space-y-4",children:(0,d.jsx)("div",{className:"space-y-3",children:T.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted rounded-lg",children:[(0,d.jsx)("div",{className:"w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium mt-0.5",children:b+1}),(0,d.jsx)("span",{children:a})]},b))})})]})]})]})}):null}}};